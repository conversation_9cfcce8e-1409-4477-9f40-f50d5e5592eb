<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>API exported by pygame.mixer &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Class Rect API exported by pygame.rect" href="rect.html" />
    <link rel="prev" title="API exported by pygame._freetype" href="freetype.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="../ref/color.html">Color</a> | 
	    <a href="../ref/display.html">display</a> | 
	    <a href="../ref/draw.html">draw</a> | 
	    <a href="../ref/event.html">event</a> | 
	    <a href="../ref/font.html">font</a> | 
	    <a href="../ref/image.html">image</a> | 
	    <a href="../ref/key.html">key</a> | 
	    <a href="../ref/locals.html">locals</a> | 
	    <a href="../ref/mixer.html">mixer</a> | 
	    <a href="../ref/mouse.html">mouse</a> | 
	    <a href="../ref/rect.html">Rect</a> | 
	    <a href="../ref/surface.html">Surface</a> | 
	    <a href="../ref/time.html">time</a> | 
	    <a href="../ref/music.html">music</a> | 
	    <a href="../ref/pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="../ref/cursors.html">cursors</a> | 
	    <a href="../ref/joystick.html">joystick</a> | 
	    <a href="../ref/mask.html">mask</a> | 
	    <a href="../ref/sprite.html">sprite</a> | 
	    <a href="../ref/transform.html">transform</a> | 
	    <a href="../ref/bufferproxy.html">BufferProxy</a> | 
	    <a href="../ref/freetype.html">freetype</a> | 
	    <a href="../ref/gfxdraw.html">gfxdraw</a> | 
	    <a href="../ref/midi.html">midi</a> | 
	    <a href="../ref/pixelarray.html">PixelArray</a> | 
	    <a href="../ref/pixelcopy.html">pixelcopy</a> | 
	    <a href="../ref/sndarray.html">sndarray</a> | 
	    <a href="../ref/surfarray.html">surfarray</a> | 
	    <a href="../ref/math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="../ref/camera.html">camera</a> | 
	    <a href="../ref/sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="../ref/examples.html">examples</a> | 
	    <a href="../ref/fastevent.html">fastevent</a> | 
	    <a href="../ref/scrap.html">scrap</a> | 
	    <a href="../ref/tests.html">tests</a> | 
	    <a href="../ref/touch.html">touch</a> | 
	    <a href="../ref/pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="api-exported-by-pygame-mixer">
<section id="src-c-mixer-c">
<h2>src_c/mixer.c<a class="headerlink" href="#src-c-mixer-c" title="Link to this heading">¶</a></h2>
<p>Python types and module startup/shutdown functions defined in the
<a class="tooltip reference internal" href="../ref/mixer.html#module-pygame.mixer" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer</span></code><span class="tooltip-content">pygame module for loading and playing sounds</span></a> extension module.</p>
<p>Header file: src_c/include/pygame_mixer.h</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.pgSoundObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgSoundObject</span></span></span><a class="headerlink" href="#c.pgSoundObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Sound" title="pygame.mixer.Sound"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Sound</span></code></a> instance C structure.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.pgSound_Type">
<span class="n"><span class="pre">PyTypeObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgSound_Type</span></span></span><a class="headerlink" href="#c.pgSound_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Sound" title="pygame.mixer.Sound"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Sound</span></code></a> Python type.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgSound_New">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgSound_New</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">Mix_Chunk</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">chunk</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgSound_New" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a new <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Sound" title="pygame.mixer.Sound"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Sound</span></code></a> instance for the SDL mixer chunk <em>chunk</em>.
On failure, raise a Python exception and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgSound_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgSound_Check</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgSound_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>obj</em> is an instance of type <a class="reference internal" href="#c.pgSound_Type" title="pgSound_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">pgSound_Type</span></code></a>,
but not a <a class="reference internal" href="#c.pgSound_Type" title="pgSound_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">pgSound_Type</span></code></a> subclass instance.
A macro.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgSound_AsChunk">
<span class="n"><span class="pre">Mix_Chunk</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgSound_AsChunk</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgSound_AsChunk" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the SDL <code class="xref c c-type docutils literal notranslate"><span class="pre">Mix_Chunk</span></code> struct associated with the
<a class="reference internal" href="#c.pgSound_Type" title="pgSound_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">pgSound_Type</span></code></a> instance <em>x</em>.
A macro that does no <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or Python type check on <em>x</em>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.pgChannelObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgChannelObject</span></span></span><a class="headerlink" href="#c.pgChannelObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Channel" title="pygame.mixer.Channel"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Channel</span></code></a> instance C structure.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.pgChannel_Type">
<span class="n"><span class="pre">PyTypeObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgChannel_Type</span></span></span><a class="headerlink" href="#c.pgChannel_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Channel" title="pygame.mixer.Channel"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Channel</span></code></a> Python type.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgChannel_New">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgChannel_New</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">channelnum</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgChannel_New" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a new <a class="reference internal" href="../ref/mixer.html#pygame.mixer.Channel" title="pygame.mixer.Channel"><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Channel</span></code></a> instance for the SDL mixer
channel <em>channelnum</em>.
On failure, raise a Python exception and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgChannel_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgChannel_Check</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgChannel_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>obj</em> is an instance of type <a class="reference internal" href="#c.pgChannel_Type" title="pgChannel_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">pgChannel_Type</span></code></a>,
but not a <a class="reference internal" href="#c.pgChannel_Type" title="pgChannel_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">pgChannel_Type</span></code></a> subclass instance.
A macro.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgChannel_AsInt">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgChannel_AsInt</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgChannel_AsInt" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the SDL mixer music channel number associated with <a class="reference internal" href="#c.pgChannel_Type" title="pgChannel_Type"><code class="xref c c-type docutils literal notranslate"><span class="pre">pgChannel_Type</span></code></a> instance <em>x</em>.
A macro that does no <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or Python type check on <em>x</em>.</p>
</dd></dl>

</section>
</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/c_api/mixer.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="rect.html" title="Class Rect API exported by pygame.rect"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="freetype.html" title="API exported by pygame._freetype"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../c_api.html" accesskey="U">pygame C API</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API exported by pygame.mixer</a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>