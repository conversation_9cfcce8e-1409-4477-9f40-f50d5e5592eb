<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.mixer &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.mouse" href="mouse.html" />
    <link rel="prev" title="pygame.midi" href="midi.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.mixer">
<span id="pygame-mixer"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.mixer</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for loading and playing sounds</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.init">pygame.mixer.init</a></div>
</td>
<td>—</td>
<td>initialize the mixer module</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.pre_init">pygame.mixer.pre_init</a></div>
</td>
<td>—</td>
<td>preset the mixer init arguments</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.quit">pygame.mixer.quit</a></div>
</td>
<td>—</td>
<td>uninitialize the mixer</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.get_init">pygame.mixer.get_init</a></div>
</td>
<td>—</td>
<td>test if the mixer is initialized</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.stop">pygame.mixer.stop</a></div>
</td>
<td>—</td>
<td>stop playback of all sound channels</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.pause">pygame.mixer.pause</a></div>
</td>
<td>—</td>
<td>temporarily stop playback of all sound channels</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.unpause">pygame.mixer.unpause</a></div>
</td>
<td>—</td>
<td>resume paused playback of sound channels</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.fadeout">pygame.mixer.fadeout</a></div>
</td>
<td>—</td>
<td>fade out the volume on all sounds before stopping</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.set_num_channels">pygame.mixer.set_num_channels</a></div>
</td>
<td>—</td>
<td>set the total number of playback channels</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.get_num_channels">pygame.mixer.get_num_channels</a></div>
</td>
<td>—</td>
<td>get the total number of playback channels</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.set_reserved">pygame.mixer.set_reserved</a></div>
</td>
<td>—</td>
<td>reserve channels from being automatically used</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.find_channel">pygame.mixer.find_channel</a></div>
</td>
<td>—</td>
<td>find an unused channel</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.get_busy">pygame.mixer.get_busy</a></div>
</td>
<td>—</td>
<td>test if any sound is being mixed</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.get_sdl_mixer_version">pygame.mixer.get_sdl_mixer_version</a></div>
</td>
<td>—</td>
<td>get the mixer's SDL version</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound">pygame.mixer.Sound</a></div>
</td>
<td>—</td>
<td>Create a new Sound object from a file or buffer object</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel">pygame.mixer.Channel</a></div>
</td>
<td>—</td>
<td>Create a Channel object for controlling playback</td>
</tr>
</tbody>
</table>
<p>This module contains classes for loading Sound objects and controlling
playback. The mixer module is optional and depends on SDL_mixer. Your program
should test that <a class="tooltip reference internal" href="#module-pygame.mixer" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer</span></code><span class="tooltip-content">pygame module for loading and playing sounds</span></a> is available and initialized before using
it.</p>
<p>The mixer module has a limited number of channels for playback of sounds.
Usually programs tell pygame to start playing audio and it selects an available
channel automatically. The default is 8 simultaneous channels, but complex
programs can get more precise control over the number of channels and their
use.</p>
<p>All sound playback is mixed in background threads. When you begin to play a
Sound object, it will return immediately while the sound continues to play. A
single Sound object can also be actively played back multiple times.</p>
<p>The mixer also has a special streaming channel. This is for music playback and
is accessed through the <a class="tooltip reference internal" href="music.html#module-pygame.mixer.music" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer.music</span></code><span class="tooltip-content">pygame module for controlling streamed audio</span></a> module. Consider using this
module for playing long running music. Unlike mixer module, the music module
streams the music from the files without loading music at once into memory.</p>
<p>The mixer module must be initialized like other pygame modules, but it has some
extra conditions. The <code class="docutils literal notranslate"><span class="pre">pygame.mixer.init()</span></code> function takes several optional
arguments to control the playback rate and sample size. Pygame will default to
reasonable values, but pygame cannot perform Sound resampling, so the mixer
should be initialized to match the values of your audio resources.</p>
<p><code class="docutils literal notranslate"><span class="pre">NOTE</span></code>: For less laggy sound use a smaller buffer size. The default
is set to reduce the chance of scratchy sounds on some computers. You can
change the default buffer by calling <a class="tooltip reference internal" href="#pygame.mixer.pre_init" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.mixer.pre_init()</span></code><span class="tooltip-content">preset the mixer init arguments</span></a> before
<a class="tooltip reference internal" href="#pygame.mixer.init" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.mixer.init()</span></code><span class="tooltip-content">initialize the mixer module</span></a> or <a class="tooltip reference internal" href="pygame.html#pygame.init" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.init()</span></code><span class="tooltip-content">initialize all imported pygame modules</span></a> is called. For example:
<code class="docutils literal notranslate"><span class="pre">pygame.mixer.pre_init(44100,-16,2,</span> <span class="pre">1024)</span></code></p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.init">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">initialize the mixer module</span></div>
<div class="line"><span class="signature">init(frequency=44100, size=-16, channels=2, buffer=512, devicename=None, allowedchanges=AUDIO_ALLOW_FREQUENCY_CHANGE | AUDIO_ALLOW_CHANNELS_CHANGE) -&gt; None</span></div>
</div>
<p>Initialize the mixer module for Sound loading and playback. The default
arguments can be overridden to provide specific audio mixing. Keyword
arguments are accepted. For backwards compatibility, argument values of
0 are replaced with the startup defaults, except for <code class="docutils literal notranslate"><span class="pre">allowedchanges</span></code>,
where -1 is used. (startup defaults may be changed by a <a class="reference internal" href="#pygame.mixer.pre_init" title="pygame.mixer.pre_init"><code class="xref py py-func docutils literal notranslate"><span class="pre">pre_init()</span></code></a> call).</p>
<p>The size argument represents how many bits are used for each audio sample.
If the value is negative then signed sample values will be used. Positive
values mean unsigned audio samples will be used. An invalid value raises an
exception.</p>
<p>The channels argument is used to specify whether to use mono or stereo. 1
for mono and 2 for stereo.</p>
<p>The buffer argument controls the number of internal samples used in the
sound mixer. The default value should work for most cases. It can be lowered
to reduce latency, but sound dropout may occur. It can be raised to larger
values to ensure playback never skips, but it will impose latency on sound
playback. The buffer size must be a power of two (if not it is rounded up to
the next nearest power of 2).</p>
<p>Some platforms require the <a class="tooltip reference internal" href="#module-pygame.mixer" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer</span></code><span class="tooltip-content">pygame module for loading and playing sounds</span></a> module to be initialized
after the display modules have initialized. The top level <code class="docutils literal notranslate"><span class="pre">pygame.init()</span></code>
takes care of this automatically, but cannot pass any arguments to the mixer
init. To solve this, mixer has a function <code class="docutils literal notranslate"><span class="pre">pygame.mixer.pre_init()</span></code> to set
the proper defaults before the toplevel init is used.</p>
<p>When using allowedchanges=0 it will convert the samples at runtime to match
what the hardware supports. For example a sound card may not
support 16bit sound samples, so instead it will use 8bit samples internally.
If AUDIO_ALLOW_FORMAT_CHANGE is supplied, then the requested format will
change to the closest that SDL2 supports.</p>
<p>Apart from 0, allowedchanged accepts the following constants ORed together:</p>
<blockquote>
<div><ul class="simple">
<li><p>AUDIO_ALLOW_FREQUENCY_CHANGE</p></li>
<li><p>AUDIO_ALLOW_FORMAT_CHANGE</p></li>
<li><p>AUDIO_ALLOW_CHANNELS_CHANGE</p></li>
<li><p>AUDIO_ALLOW_ANY_CHANGE</p></li>
</ul>
</div></blockquote>
<p>It is safe to call this more than once, but after the mixer is initialized
you cannot change the playback arguments without first calling
<code class="docutils literal notranslate"><span class="pre">pygame.mixer.quit()</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.8: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 1024 to 3072.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.9.1: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 3072 to 4096.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 4096 to 512.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span>The default <code class="docutils literal notranslate"><span class="pre">frequency</span></code> changed from 22050 to 44100.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span><code class="docutils literal notranslate"><span class="pre">size</span></code> can be 32 (32-bit floats).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span><code class="docutils literal notranslate"><span class="pre">channels</span></code> can also be 4 or 6.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.0: </span><code class="docutils literal notranslate"><span class="pre">allowedchanges</span></code>, <code class="docutils literal notranslate"><span class="pre">devicename</span></code> arguments added</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.pre_init">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">pre_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.pre_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">preset the mixer init arguments</span></div>
<div class="line"><span class="signature">pre_init(frequency=44100, size=-16, channels=2, buffer=512, devicename=None, allowedchanges=AUDIO_ALLOW_FREQUENCY_CHANGE | AUDIO_ALLOW_CHANNELS_CHANGE) -&gt; None</span></div>
</div>
<p>Call pre_init to change the defaults used when the real
<code class="docutils literal notranslate"><span class="pre">pygame.mixer.init()</span></code> is called. Keyword arguments are accepted. The best
way to set custom mixer playback values is to call
<code class="docutils literal notranslate"><span class="pre">pygame.mixer.pre_init()</span></code> before calling the top level <code class="docutils literal notranslate"><span class="pre">pygame.init()</span></code>.
For backwards compatibility, argument values of 0 are replaced with the
startup defaults, except for <code class="docutils literal notranslate"><span class="pre">allowedchanges</span></code>, where -1 is used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.8: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 1024 to 3072.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.9.1: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 3072 to 4096.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span>The default <code class="docutils literal notranslate"><span class="pre">buffersize</span></code> changed from 4096 to 512.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span>The default <code class="docutils literal notranslate"><span class="pre">frequency</span></code> changed from 22050 to 44100.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.0: </span><code class="docutils literal notranslate"><span class="pre">allowedchanges</span></code>, <code class="docutils literal notranslate"><span class="pre">devicename</span></code> arguments added</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.quit">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.quit" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">uninitialize the mixer</span></div>
<div class="line"><span class="signature">quit() -&gt; None</span></div>
</div>
<p>This will uninitialize <a class="tooltip reference internal" href="#module-pygame.mixer" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer</span></code><span class="tooltip-content">pygame module for loading and playing sounds</span></a>. All playback will stop and any
loaded Sound objects may not be compatible with the mixer if it is
reinitialized later.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.get_init">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">test if the mixer is initialized</span></div>
<div class="line"><span class="signature">get_init() -&gt; (frequency, format, channels)</span></div>
</div>
<p>If the mixer is initialized, this returns the playback arguments it is
using. If the mixer has not been initialized this returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.stop">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.stop" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop playback of all sound channels</span></div>
<div class="line"><span class="signature">stop() -&gt; None</span></div>
</div>
<p>This will stop all playback of all active mixer channels.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.pause">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">pause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.pause" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">temporarily stop playback of all sound channels</span></div>
<div class="line"><span class="signature">pause() -&gt; None</span></div>
</div>
<p>This will temporarily stop all playback on the active mixer channels. The
playback can later be resumed with <code class="docutils literal notranslate"><span class="pre">pygame.mixer.unpause()</span></code></p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.unpause">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">unpause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.unpause" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">resume paused playback of sound channels</span></div>
<div class="line"><span class="signature">unpause() -&gt; None</span></div>
</div>
<p>This will resume all active sound channels after they have been paused.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.fadeout">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">fadeout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.fadeout" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">fade out the volume on all sounds before stopping</span></div>
<div class="line"><span class="signature">fadeout(time) -&gt; None</span></div>
</div>
<p>This will fade out the volume on all active channels over the time argument
in milliseconds. After the sound is muted the playback will stop.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.set_num_channels">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">set_num_channels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.set_num_channels" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">set the total number of playback channels</span></div>
<div class="line"><span class="signature">set_num_channels(count) -&gt; None</span></div>
</div>
<p>Sets the number of available channels for the mixer. The default value is 8.
The value can be increased or decreased. If the value is decreased, sounds
playing on the truncated channels are stopped.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.get_num_channels">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">get_num_channels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.get_num_channels" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the total number of playback channels</span></div>
<div class="line"><span class="signature">get_num_channels() -&gt; count</span></div>
</div>
<p>Returns the number of currently active playback channels.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.set_reserved">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">set_reserved</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.set_reserved" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">reserve channels from being automatically used</span></div>
<div class="line"><span class="signature">set_reserved(count) -&gt; count</span></div>
</div>
<p>The mixer can reserve any number of channels that will not be automatically
selected for playback by Sounds. This means that whenever you play a Sound
without specifying a channel, a reserved channel will never be used. If sounds
are currently playing on the reserved channels they will not be stopped.</p>
<p>This allows the application to reserve a specific number of channels for
important sounds that must not be dropped or have a guaranteed channel to
play on.</p>
<p>Will return number of channels actually reserved, this may be less than requested
depending on the number of channels previously allocated.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.find_channel">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">find_channel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.find_channel" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">find an unused channel</span></div>
<div class="line"><span class="signature">find_channel(force=False) -&gt; Channel</span></div>
</div>
<p>This will find and return an inactive Channel object. If there are no
inactive Channels this function will return <code class="docutils literal notranslate"><span class="pre">None</span></code>. If there are no
inactive channels and the force argument is <code class="docutils literal notranslate"><span class="pre">True</span></code>, this will find the
Channel with the longest running Sound and return it.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.get_busy">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">get_busy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.get_busy" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">test if any sound is being mixed</span></div>
<div class="line"><span class="signature">get_busy() -&gt; bool</span></div>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the mixer is busy mixing any channels. If the mixer is
idle then this return <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.mixer.get_sdl_mixer_version">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">get_sdl_mixer_version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.get_sdl_mixer_version" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the mixer's SDL version</span></div>
<div class="line"><span class="signature">get_sdl_mixer_version() -&gt; (major, minor, patch)</span></div>
<div class="line"><span class="signature">get_sdl_mixer_version(linked=True) -&gt; (major, minor, patch)</span></div>
</div>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>linked</strong> (<em>bool</em>) -- if <code class="docutils literal notranslate"><span class="pre">True</span></code> (default) the linked version number is
returned, otherwise the compiled version number is returned</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>the mixer's SDL library version number (linked or compiled
depending on the <code class="docutils literal notranslate"><span class="pre">linked</span></code> parameter) as a tuple of 3 integers
<code class="docutils literal notranslate"><span class="pre">(major,</span> <span class="pre">minor,</span> <span class="pre">patch)</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>tuple</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The linked and compile version numbers should be the same.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.0.</span></p>
</div>
</dd></dl>

<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">Sound</span></span><a class="headerlink" href="#pygame.mixer.Sound" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Create a new Sound object from a file or buffer object</span></div>
<div class="line"><span class="signature">Sound(filename) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(file=filename) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(file=pathlib_path) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(buffer) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(buffer=buffer) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(object) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(file=object) -&gt; Sound</span></div>
<div class="line"><span class="signature">Sound(array=object) -&gt; Sound</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.play">pygame.mixer.Sound.play</a></div>
</td>
<td>—</td>
<td>begin sound playback</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.stop">pygame.mixer.Sound.stop</a></div>
</td>
<td>—</td>
<td>stop sound playback</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.fadeout">pygame.mixer.Sound.fadeout</a></div>
</td>
<td>—</td>
<td>stop sound playback after fading out</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.set_volume">pygame.mixer.Sound.set_volume</a></div>
</td>
<td>—</td>
<td>set the playback volume for this Sound</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.get_volume">pygame.mixer.Sound.get_volume</a></div>
</td>
<td>—</td>
<td>get the playback volume</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.get_num_channels">pygame.mixer.Sound.get_num_channels</a></div>
</td>
<td>—</td>
<td>count how many times this Sound is playing</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.get_length">pygame.mixer.Sound.get_length</a></div>
</td>
<td>—</td>
<td>get the length of the Sound</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Sound.get_raw">pygame.mixer.Sound.get_raw</a></div>
</td>
<td>—</td>
<td>return a bytestring copy of the Sound samples.</td>
</tr>
</tbody>
</table>
<p>Load a new sound buffer from a filename, a python file object or a readable
buffer object. Limited resampling will be performed to help the sample match
the initialize arguments for the mixer. A Unicode string can only be a file
pathname. A bytes object can be either a pathname or a buffer object.
Use the 'file' or 'buffer' keywords to avoid ambiguity; otherwise Sound may
guess wrong. If the array keyword is used, the object is expected to export
a new buffer interface (The object is checked for a buffer interface first.)</p>
<p>The Sound object represents actual sound sample data. Methods that change
the state of the Sound object will the all instances of the Sound playback.
A Sound object also exports a new buffer interface.</p>
<p>The Sound can be loaded from an <code class="docutils literal notranslate"><span class="pre">OGG</span></code> audio file or from an uncompressed
<code class="docutils literal notranslate"><span class="pre">WAV</span></code>.</p>
<p>Note: The buffer will be copied internally, no data will be shared between
it and the Sound object.</p>
<p>For now buffer and array support is consistent with <code class="docutils literal notranslate"><span class="pre">sndarray.make_sound</span></code>
for NumPy arrays, in that sample sign and byte order are ignored. This
will change, either by correctly handling sign and byte order, or by raising
an exception when different. Also, source samples are truncated to fit the
audio sample size. This will not change.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.8: </span><code class="docutils literal notranslate"><span class="pre">pygame.mixer.Sound(buffer)</span></code></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.9.2: </span><a class="tooltip reference internal" href="#pygame.mixer.Sound" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.mixer.Sound</span></code><span class="tooltip-content">Create a new Sound object from a file or buffer object</span></a> keyword arguments and array interface support</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.1: </span>pathlib.Path support on Python 3.</p>
</div>
<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.play">
<span class="sig-name descname"><span class="pre">play</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.play" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">begin sound playback</span></div>
<div class="line"><span class="signature">play(loops=0, maxtime=0, fade_ms=0) -&gt; Channel</span></div>
</div>
<p>Begin playback of the Sound (i.e., on the computer's speakers) on an
available Channel. This will forcibly select a Channel, so playback may
cut off a currently playing sound if necessary.</p>
<p>The loops argument controls how many times the sample will be repeated
after being played the first time. A value of 5 means that the sound will
be played once, then repeated five times, and so is played a total of six
times. The default value (zero) means the Sound is not repeated, and so
is only played once. If loops is set to -1 the Sound will loop
indefinitely (though you can still call <code class="docutils literal notranslate"><span class="pre">stop()</span></code> to stop it).</p>
<p>The maxtime argument can be used to stop playback after a given number of
milliseconds.</p>
<p>The fade_ms argument will make the sound start playing at 0 volume and
fade up to full volume over the time given. The sample may end before the
fade-in is complete.</p>
<p>This returns the Channel object for the channel that was selected.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.stop" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop sound playback</span></div>
<div class="line"><span class="signature">stop() -&gt; None</span></div>
</div>
<p>This will stop the playback of this Sound on any active Channels.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.fadeout">
<span class="sig-name descname"><span class="pre">fadeout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.fadeout" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop sound playback after fading out</span></div>
<div class="line"><span class="signature">fadeout(time) -&gt; None</span></div>
</div>
<p>This will stop playback of the sound after fading it out over the time
argument in milliseconds. The Sound will fade and stop on all actively
playing channels.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.set_volume">
<span class="sig-name descname"><span class="pre">set_volume</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.set_volume" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">set the playback volume for this Sound</span></div>
<div class="line"><span class="signature">set_volume(value) -&gt; None</span></div>
</div>
<p>This will set the playback volume (loudness) for this Sound. This will
immediately affect the Sound if it is playing. It will also affect any
future playback of this Sound.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>value</strong> (<em>float</em>) -- <p>volume in the range of 0.0 to 1.0 (inclusive)</p>
<div class="line-block">
<div class="line">If value &lt; 0.0, the volume will not be changed</div>
<div class="line">If value &gt; 1.0, the volume will be set to 1.0</div>
</div>
</p>
</dd>
</dl>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.get_volume">
<span class="sig-name descname"><span class="pre">get_volume</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.get_volume" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the playback volume</span></div>
<div class="line"><span class="signature">get_volume() -&gt; value</span></div>
</div>
<p>Return a value from 0.0 to 1.0 representing the volume for this Sound.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.get_num_channels">
<span class="sig-name descname"><span class="pre">get_num_channels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.get_num_channels" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">count how many times this Sound is playing</span></div>
<div class="line"><span class="signature">get_num_channels() -&gt; count</span></div>
</div>
<p>Return the number of active channels this sound is playing on.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.get_length">
<span class="sig-name descname"><span class="pre">get_length</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.get_length" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the length of the Sound</span></div>
<div class="line"><span class="signature">get_length() -&gt; seconds</span></div>
</div>
<p>Return the length of this Sound in seconds.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Sound.get_raw">
<span class="sig-name descname"><span class="pre">get_raw</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Sound.get_raw" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">return a bytestring copy of the Sound samples.</span></div>
<div class="line"><span class="signature">get_raw() -&gt; bytes</span></div>
</div>
<p>Return a copy of the Sound object buffer as a bytes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.9.2.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel">
<span class="sig-prename descclassname"><span class="pre">pygame.mixer.</span></span><span class="sig-name descname"><span class="pre">Channel</span></span><a class="headerlink" href="#pygame.mixer.Channel" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Create a Channel object for controlling playback</span></div>
<div class="line"><span class="signature">Channel(id) -&gt; Channel</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.play">pygame.mixer.Channel.play</a></div>
</td>
<td>—</td>
<td>play a Sound on a specific Channel</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.stop">pygame.mixer.Channel.stop</a></div>
</td>
<td>—</td>
<td>stop playback on a Channel</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.pause">pygame.mixer.Channel.pause</a></div>
</td>
<td>—</td>
<td>temporarily stop playback of a channel</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.unpause">pygame.mixer.Channel.unpause</a></div>
</td>
<td>—</td>
<td>resume pause playback of a channel</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.fadeout">pygame.mixer.Channel.fadeout</a></div>
</td>
<td>—</td>
<td>stop playback after fading channel out</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.set_volume">pygame.mixer.Channel.set_volume</a></div>
</td>
<td>—</td>
<td>set the volume of a playing channel</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.get_volume">pygame.mixer.Channel.get_volume</a></div>
</td>
<td>—</td>
<td>get the volume of the playing channel</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.get_busy">pygame.mixer.Channel.get_busy</a></div>
</td>
<td>—</td>
<td>check if the channel is active</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.get_sound">pygame.mixer.Channel.get_sound</a></div>
</td>
<td>—</td>
<td>get the currently playing Sound</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.queue">pygame.mixer.Channel.queue</a></div>
</td>
<td>—</td>
<td>queue a Sound object to follow the current</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.get_queue">pygame.mixer.Channel.get_queue</a></div>
</td>
<td>—</td>
<td>return any Sound that is queued</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.set_endevent">pygame.mixer.Channel.set_endevent</a></div>
</td>
<td>—</td>
<td>have the channel send an event when playback stops</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="mixer.html#pygame.mixer.Channel.get_endevent">pygame.mixer.Channel.get_endevent</a></div>
</td>
<td>—</td>
<td>get the event a channel sends when playback stops</td>
</tr>
</tbody>
</table>
<p>Return a Channel object for one of the current channels. The id must be a
value from 0 to the value of <code class="docutils literal notranslate"><span class="pre">pygame.mixer.get_num_channels()</span></code>.</p>
<p>The Channel object can be used to get fine control over the playback of
Sounds. A channel can only playback a single Sound at time. Using channels
is entirely optional since pygame can manage them by default.</p>
<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.play">
<span class="sig-name descname"><span class="pre">play</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.play" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">play a Sound on a specific Channel</span></div>
<div class="line"><span class="signature">play(Sound, loops=0, maxtime=0, fade_ms=0) -&gt; None</span></div>
</div>
<p>This will begin playback of a Sound on a specific Channel. If the Channel
is currently playing any other Sound it will be stopped.</p>
<p>The loops argument has the same meaning as in <code class="docutils literal notranslate"><span class="pre">Sound.play()</span></code>: it is the
number of times to repeat the sound after the first time. If it is 3, the
sound will be played 4 times (the first time, then three more). If loops
is -1 then the playback will repeat indefinitely.</p>
<p>As in <code class="docutils literal notranslate"><span class="pre">Sound.play()</span></code>, the maxtime argument can be used to stop playback
of the Sound after a given number of milliseconds.</p>
<p>As in <code class="docutils literal notranslate"><span class="pre">Sound.play()</span></code>, the fade_ms argument can be used fade in the
sound.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.stop" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop playback on a Channel</span></div>
<div class="line"><span class="signature">stop() -&gt; None</span></div>
</div>
<p>Stop sound playback on a channel. After playback is stopped the channel
becomes available for new Sounds to play on it.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.pause">
<span class="sig-name descname"><span class="pre">pause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.pause" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">temporarily stop playback of a channel</span></div>
<div class="line"><span class="signature">pause() -&gt; None</span></div>
</div>
<p>Temporarily stop the playback of sound on a channel. It can be resumed at
a later time with <code class="docutils literal notranslate"><span class="pre">Channel.unpause()</span></code></p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.unpause">
<span class="sig-name descname"><span class="pre">unpause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.unpause" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">resume pause playback of a channel</span></div>
<div class="line"><span class="signature">unpause() -&gt; None</span></div>
</div>
<p>Resume the playback on a paused channel.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.fadeout">
<span class="sig-name descname"><span class="pre">fadeout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.fadeout" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop playback after fading channel out</span></div>
<div class="line"><span class="signature">fadeout(time) -&gt; None</span></div>
</div>
<p>Stop playback of a channel after fading out the sound over the given time
argument in milliseconds.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.set_volume">
<span class="sig-name descname"><span class="pre">set_volume</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.set_volume" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">set the volume of a playing channel</span></div>
<div class="line"><span class="signature">set_volume(value) -&gt; None</span></div>
<div class="line"><span class="signature">set_volume(left, right) -&gt; None</span></div>
</div>
<p>Set the volume (loudness) of a playing sound. When a channel starts to
play its volume value is reset. This only affects the current sound. The
value argument is between 0.0 and 1.0.</p>
<p>If one argument is passed, it will be the volume of both speakers. If two
arguments are passed and the mixer is in stereo mode, the first argument
will be the volume of the left speaker and the second will be the volume
of the right speaker. (If the second argument is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the first
argument will be the volume of both speakers.)</p>
<p>If the channel is playing a Sound on which <code class="docutils literal notranslate"><span class="pre">set_volume()</span></code> has also been
called, both calls are taken into account. For example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">sound</span> <span class="o">=</span> <span class="n">pygame</span><span class="o">.</span><span class="n">mixer</span><span class="o">.</span><span class="n">Sound</span><span class="p">(</span><span class="s2">&quot;s.wav&quot;</span><span class="p">)</span>
<span class="n">channel</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">play</span><span class="p">()</span>      <span class="c1"># Sound plays at full volume by default</span>
<span class="n">sound</span><span class="o">.</span><span class="n">set_volume</span><span class="p">(</span><span class="mf">0.9</span><span class="p">)</span>   <span class="c1"># Now plays at 90% of full volume.</span>
<span class="n">sound</span><span class="o">.</span><span class="n">set_volume</span><span class="p">(</span><span class="mf">0.6</span><span class="p">)</span>   <span class="c1"># Now plays at 60% (previous value replaced).</span>
<span class="n">channel</span><span class="o">.</span><span class="n">set_volume</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span> <span class="c1"># Now plays at 30% (0.6 * 0.5).</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.get_volume">
<span class="sig-name descname"><span class="pre">get_volume</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.get_volume" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the volume of the playing channel</span></div>
<div class="line"><span class="signature">get_volume() -&gt; value</span></div>
</div>
<p>Return the volume of the channel for the current playing sound. This does
not take into account stereo separation used by
<a class="reference internal" href="#pygame.mixer.Channel.set_volume" title="pygame.mixer.Channel.set_volume"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Channel.set_volume()</span></code></a>. The Sound object also has its own volume
which is mixed with the channel.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.get_busy">
<span class="sig-name descname"><span class="pre">get_busy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.get_busy" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">check if the channel is active</span></div>
<div class="line"><span class="signature">get_busy() -&gt; bool</span></div>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the channel is actively mixing sound. If the channel
is idle this returns <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.get_sound">
<span class="sig-name descname"><span class="pre">get_sound</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.get_sound" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the currently playing Sound</span></div>
<div class="line"><span class="signature">get_sound() -&gt; Sound</span></div>
</div>
<p>Return the actual Sound object currently playing on this channel. If the
channel is idle <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.queue">
<span class="sig-name descname"><span class="pre">queue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.queue" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">queue a Sound object to follow the current</span></div>
<div class="line"><span class="signature">queue(Sound) -&gt; None</span></div>
</div>
<p>When a Sound is queued on a Channel, it will begin playing immediately
after the current Sound is finished. Each channel can only have a single
Sound queued at a time. The queued Sound will only play if the current
playback finished automatically. It is cleared on any other call to
<code class="docutils literal notranslate"><span class="pre">Channel.stop()</span></code> or <code class="docutils literal notranslate"><span class="pre">Channel.play()</span></code>.</p>
<p>If there is no sound actively playing on the Channel then the Sound will
begin playing immediately.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.get_queue">
<span class="sig-name descname"><span class="pre">get_queue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.get_queue" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">return any Sound that is queued</span></div>
<div class="line"><span class="signature">get_queue() -&gt; Sound</span></div>
</div>
<p>If a Sound is already queued on this channel it will be returned. Once
the queued sound begins playback it will no longer be on the queue.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.set_endevent">
<span class="sig-name descname"><span class="pre">set_endevent</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.set_endevent" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">have the channel send an event when playback stops</span></div>
<div class="line"><span class="signature">set_endevent() -&gt; None</span></div>
<div class="line"><span class="signature">set_endevent(type) -&gt; None</span></div>
</div>
<p>When an endevent is set for a channel, it will send an event to the
pygame queue every time a sound finishes playing on that channel (not
just the first time). Use <code class="docutils literal notranslate"><span class="pre">pygame.event.get()</span></code> to retrieve the endevent
once it's sent.</p>
<p>Note that if you called <code class="docutils literal notranslate"><span class="pre">Sound.play(n)</span></code> or <code class="docutils literal notranslate"><span class="pre">Channel.play(sound,n)</span></code>,
the end event is sent only once: after the sound has been played &quot;n+1&quot;
times (see the documentation of Sound.play).</p>
<p>If <code class="docutils literal notranslate"><span class="pre">Channel.stop()</span></code> or <code class="docutils literal notranslate"><span class="pre">Channel.play()</span></code> is called while the sound was
still playing, the event will be posted immediately.</p>
<p>The type argument will be the event id sent to the queue. This can be any
valid event type, but a good choice would be a value between
<code class="docutils literal notranslate"><span class="pre">pygame.locals.USEREVENT</span></code> and <code class="docutils literal notranslate"><span class="pre">pygame.locals.NUMEVENTS</span></code>. If no type
argument is given then the Channel will stop sending endevents.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.mixer.Channel.get_endevent">
<span class="sig-name descname"><span class="pre">get_endevent</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.mixer.Channel.get_endevent" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get the event a channel sends when playback stops</span></div>
<div class="line"><span class="signature">get_endevent() -&gt; type</span></div>
</div>
<p>Returns the event type to be sent every time the Channel finishes
playback of a Sound. If there is no endevent the function returns
<code class="docutils literal notranslate"><span class="pre">pygame.NOEVENT</span></code>.</p>
</dd></dl>

</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref/mixer.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mouse.html" title="pygame.mouse"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="midi.html" title="pygame.midi"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.mixer</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>