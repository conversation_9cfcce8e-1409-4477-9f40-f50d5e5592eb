<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.Color &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Named Colors" href="color_list.html" />
    <link rel="prev" title="pygame.cdrom" href="cdrom.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="pygame-color">
<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame.Color">
<span class="sig-prename descclassname"><span class="pre">pygame.</span></span><span class="sig-name descname"><span class="pre">Color</span></span><a class="headerlink" href="#pygame.Color" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame object for color representations</span></div>
<div class="line"><span class="signature">Color(r, g, b) -&gt; Color</span></div>
<div class="line"><span class="signature">Color(r, g, b, a=255) -&gt; Color</span></div>
<div class="line"><span class="signature">Color(color_value) -&gt; Color</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.r">pygame.Color.r</a></div>
</td>
<td>—</td>
<td>Gets or sets the red value of the Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.g">pygame.Color.g</a></div>
</td>
<td>—</td>
<td>Gets or sets the green value of the Color.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.b">pygame.Color.b</a></div>
</td>
<td>—</td>
<td>Gets or sets the blue value of the Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.a">pygame.Color.a</a></div>
</td>
<td>—</td>
<td>Gets or sets the alpha value of the Color.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.cmy">pygame.Color.cmy</a></div>
</td>
<td>—</td>
<td>Gets or sets the CMY representation of the Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.hsva">pygame.Color.hsva</a></div>
</td>
<td>—</td>
<td>Gets or sets the HSVA representation of the Color.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.hsla">pygame.Color.hsla</a></div>
</td>
<td>—</td>
<td>Gets or sets the HSLA representation of the Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.i1i2i3">pygame.Color.i1i2i3</a></div>
</td>
<td>—</td>
<td>Gets or sets the I1I2I3 representation of the Color.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.normalize">pygame.Color.normalize</a></div>
</td>
<td>—</td>
<td>Returns the normalized RGBA values of the Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.correct_gamma">pygame.Color.correct_gamma</a></div>
</td>
<td>—</td>
<td>Applies a certain gamma value to the Color.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.set_length">pygame.Color.set_length</a></div>
</td>
<td>—</td>
<td>Set the number of elements in the Color to 1,2,3, or 4.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.grayscale">pygame.Color.grayscale</a></div>
</td>
<td>—</td>
<td>returns the grayscale of a Color</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.lerp">pygame.Color.lerp</a></div>
</td>
<td>—</td>
<td>returns a linear interpolation to the given Color.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.premul_alpha">pygame.Color.premul_alpha</a></div>
</td>
<td>—</td>
<td>returns a Color where the r,g,b components have been multiplied by the alpha.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="color.html#pygame.Color.update">pygame.Color.update</a></div>
</td>
<td>—</td>
<td>Sets the elements of the color</td>
</tr>
</tbody>
</table>
<p>The <code class="docutils literal notranslate"><span class="pre">Color</span></code> class represents <code class="docutils literal notranslate"><span class="pre">RGBA</span></code> color values using a value range of
0 to 255 inclusive. It allows basic arithmetic operations — binary
operations <code class="docutils literal notranslate"><span class="pre">+</span></code>, <code class="docutils literal notranslate"><span class="pre">-</span></code>, <code class="docutils literal notranslate"><span class="pre">*</span></code>, <code class="docutils literal notranslate"><span class="pre">//</span></code>, <code class="docutils literal notranslate"><span class="pre">%</span></code>, and unary operation <code class="docutils literal notranslate"><span class="pre">~</span></code> — to
create new colors, supports conversions to other color spaces such as <code class="docutils literal notranslate"><span class="pre">HSV</span></code>
or <code class="docutils literal notranslate"><span class="pre">HSL</span></code> and lets you adjust single color channels.
Alpha defaults to 255 (fully opaque) when not given.
The arithmetic operations and <code class="docutils literal notranslate"><span class="pre">correct_gamma()</span></code> method preserve subclasses.
For the binary operators, the class of the returned color is that of the
left hand color object of the operator.</p>
<p>Color objects support equality comparison with other color objects and 3 or
4 element tuples of integers. There was a bug in pygame 1.8.1
where the default alpha was 0, not 255 like previously.</p>
<p>Color objects export the C level array interface. The interface exports a
read-only one dimensional unsigned byte array of the same assigned length
as the color. The new buffer interface is also exported, with the same
characteristics as the array interface.</p>
<p>The floor division, <code class="docutils literal notranslate"><span class="pre">//</span></code>, and modulus, <code class="docutils literal notranslate"><span class="pre">%</span></code>, operators do not raise
an exception for division by zero. Instead, if a color, or alpha, channel
in the right hand color is 0, then the result is 0. For example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1"># These expressions are True</span>
<span class="n">Color</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span> <span class="o">//</span> <span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">)</span> <span class="o">==</span> <span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="n">Color</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span> <span class="o">%</span> <span class="n">Color</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="o">==</span> <span class="n">Color</span><span class="p">(</span><span class="mi">63</span><span class="p">,</span> <span class="mi">63</span><span class="p">,</span> <span class="mi">63</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>Use <code class="docutils literal notranslate"><span class="pre">int(color)</span></code> to return the immutable integer value of the color,
usable as a <code class="docutils literal notranslate"><span class="pre">dict</span></code> key. This integer value differs from the mapped
pixel values of <a class="tooltip reference internal" href="surface.html#pygame.Surface.get_at_mapped" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Surface.get_at_mapped()</span></code><span class="tooltip-content">get the mapped color value at a single pixel</span></a>,
<a class="tooltip reference internal" href="surface.html#pygame.Surface.map_rgb" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Surface.map_rgb()</span></code><span class="tooltip-content">convert a color into a mapped color value</span></a> and <a class="tooltip reference internal" href="surface.html#pygame.Surface.unmap_rgb" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Surface.unmap_rgb()</span></code><span class="tooltip-content">convert a mapped integer color value into a Color</span></a>.
It can be passed as a <code class="docutils literal notranslate"><span class="pre">color_value</span></code> argument to <a class="reference internal" href="#pygame.Color" title="pygame.Color"><code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code></a>
(useful with sets).</p>
<p>See <a class="reference internal" href="color_list.html"><span class="doc">Named Colors</span></a> for samples of the available named colors.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>r</strong> (<em>int</em>) -- red value in the range of 0 to 255 inclusive</p></li>
<li><p><strong>g</strong> (<em>int</em>) -- green value in the range of 0 to 255 inclusive</p></li>
<li><p><strong>b</strong> (<em>int</em>) -- blue value in the range of 0 to 255 inclusive</p></li>
<li><p><strong>a</strong> (<em>int</em>) -- (optional) alpha value in the range of 0 to 255 inclusive,
default is 255</p></li>
<li><p><strong>color_value</strong> (<a class="reference internal" href="#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>str</em><em> or </em><em>int</em><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>) or
</em><em>list</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- <p>color value (see note below for the supported formats)</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<dl>
<dt>Supported <code class="docutils literal notranslate"><span class="pre">color_value</span></code> formats:</dt><dd><div class="line-block">
<div class="line">- <strong>Color object:</strong> clones the given <a class="reference internal" href="#pygame.Color" title="pygame.Color"><code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code></a> object</div>
<div class="line">- <strong>Color name: str:</strong> name of the color to use, e.g. <code class="docutils literal notranslate"><span class="pre">'red'</span></code>
(all the supported name strings can be found in the
 <a class="reference internal" href="color_list.html"><span class="doc">Named Colors</span></a>, with sample swatches)</div>
<div class="line">- <strong>HTML color format str:</strong> <code class="docutils literal notranslate"><span class="pre">'#rrggbbaa'</span></code> or <code class="docutils literal notranslate"><span class="pre">'#rrggbb'</span></code>,
where rr, gg, bb, and aa are 2-digit hex numbers in the range
of 0 to 0xFF inclusive, the aa (alpha) value defaults to 0xFF
if not provided</div>
<div class="line">- <strong>hex number str:</strong> <code class="docutils literal notranslate"><span class="pre">'0xrrggbbaa'</span></code> or <code class="docutils literal notranslate"><span class="pre">'0xrrggbb'</span></code>, where
rr, gg, bb, and aa are 2-digit hex numbers in the range of 0x00
to 0xFF inclusive, the aa (alpha) value defaults to 0xFF if not
provided</div>
<div class="line">- <strong>int:</strong> int value of the color to use, using hex numbers can
make this parameter more readable, e.g. <code class="docutils literal notranslate"><span class="pre">0xrrggbbaa</span></code>, where rr,
gg, bb, and aa are 2-digit hex numbers in the range of 0x00 to
0xFF inclusive, note that the aa (alpha) value is not optional for
the int format and must be provided</div>
<div class="line">- <strong>tuple/list of int color values:</strong> <code class="docutils literal notranslate"><span class="pre">(R,</span> <span class="pre">G,</span> <span class="pre">B,</span> <span class="pre">A)</span></code> or
<code class="docutils literal notranslate"><span class="pre">(R,</span> <span class="pre">G,</span> <span class="pre">B)</span></code>, where R, G, B, and A are int values in the range of
0 to 255 inclusive, the A (alpha) value defaults to 255 if not
provided</div>
</div>
</dd>
</dl>
</div>
</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>a newly created <a class="reference internal" href="#pygame.Color" title="pygame.Color"><code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code></a> object</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="#pygame.Color" title="pygame.Color">Color</a></p>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.0: </span>Support for tuples, lists, and <a class="reference internal" href="#pygame.Color" title="pygame.Color"><code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code></a> objects when creating
<a class="reference internal" href="#pygame.Color" title="pygame.Color"><code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code></a> objects.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.9.2: </span>Color objects export the C level array interface.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.9.0: </span>Color objects support 4-element tuples of integers.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 1.8.1: </span>New implementation of the class.</p>
</div>
<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.r">
<span class="sig-name descname"><span class="pre">r</span></span><a class="headerlink" href="#pygame.Color.r" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the red value of the Color.</span></div>
<div class="line"><span class="signature">r -&gt; int</span></div>
</div>
<p>The red value of the Color.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.g">
<span class="sig-name descname"><span class="pre">g</span></span><a class="headerlink" href="#pygame.Color.g" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the green value of the Color.</span></div>
<div class="line"><span class="signature">g -&gt; int</span></div>
</div>
<p>The green value of the Color.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.b">
<span class="sig-name descname"><span class="pre">b</span></span><a class="headerlink" href="#pygame.Color.b" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the blue value of the Color.</span></div>
<div class="line"><span class="signature">b -&gt; int</span></div>
</div>
<p>The blue value of the Color.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.a">
<span class="sig-name descname"><span class="pre">a</span></span><a class="headerlink" href="#pygame.Color.a" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the alpha value of the Color.</span></div>
<div class="line"><span class="signature">a -&gt; int</span></div>
</div>
<p>The alpha value of the Color.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.cmy">
<span class="sig-name descname"><span class="pre">cmy</span></span><a class="headerlink" href="#pygame.Color.cmy" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the CMY representation of the Color.</span></div>
<div class="line"><span class="signature">cmy -&gt; tuple</span></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">CMY</span></code> representation of the Color. The <code class="docutils literal notranslate"><span class="pre">CMY</span></code> components are in
the ranges <code class="docutils literal notranslate"><span class="pre">C</span></code> = [0, 1], <code class="docutils literal notranslate"><span class="pre">M</span></code> = [0, 1], <code class="docutils literal notranslate"><span class="pre">Y</span></code> = [0, 1]. Note that this
will not return the absolutely exact <code class="docutils literal notranslate"><span class="pre">CMY</span></code> values for the set <code class="docutils literal notranslate"><span class="pre">RGB</span></code>
values in all cases. Due to the <code class="docutils literal notranslate"><span class="pre">RGB</span></code> mapping from 0-255 and the
<code class="docutils literal notranslate"><span class="pre">CMY</span></code> mapping from 0-1 rounding errors may cause the <code class="docutils literal notranslate"><span class="pre">CMY</span></code> values to
differ slightly from what you might expect.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.hsva">
<span class="sig-name descname"><span class="pre">hsva</span></span><a class="headerlink" href="#pygame.Color.hsva" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the HSVA representation of the Color.</span></div>
<div class="line"><span class="signature">hsva -&gt; tuple</span></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">HSVA</span></code> representation of the Color. The <code class="docutils literal notranslate"><span class="pre">HSVA</span></code> components are in
the ranges <code class="docutils literal notranslate"><span class="pre">H</span></code> = [0, 360], <code class="docutils literal notranslate"><span class="pre">S</span></code> = [0, 100], <code class="docutils literal notranslate"><span class="pre">V</span></code> = [0, 100], A = [0,
100]. Note that this will not return the absolutely exact <code class="docutils literal notranslate"><span class="pre">HSV</span></code> values
for the set <code class="docutils literal notranslate"><span class="pre">RGB</span></code> values in all cases. Due to the <code class="docutils literal notranslate"><span class="pre">RGB</span></code> mapping from
0-255 and the <code class="docutils literal notranslate"><span class="pre">HSV</span></code> mapping from 0-100 and 0-360 rounding errors may
cause the <code class="docutils literal notranslate"><span class="pre">HSV</span></code> values to differ slightly from what you might expect.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.hsla">
<span class="sig-name descname"><span class="pre">hsla</span></span><a class="headerlink" href="#pygame.Color.hsla" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the HSLA representation of the Color.</span></div>
<div class="line"><span class="signature">hsla -&gt; tuple</span></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">HSLA</span></code> representation of the Color. The <code class="docutils literal notranslate"><span class="pre">HSLA</span></code> components are in
the ranges <code class="docutils literal notranslate"><span class="pre">H</span></code> = [0, 360], <code class="docutils literal notranslate"><span class="pre">S</span></code> = [0, 100], <code class="docutils literal notranslate"><span class="pre">L</span></code> = [0, 100], A = [0,
100]. Note that this will not return the absolutely exact <code class="docutils literal notranslate"><span class="pre">HSL</span></code> values
for the set <code class="docutils literal notranslate"><span class="pre">RGB</span></code> values in all cases. Due to the <code class="docutils literal notranslate"><span class="pre">RGB</span></code> mapping from
0-255 and the <code class="docutils literal notranslate"><span class="pre">HSL</span></code> mapping from 0-100 and 0-360 rounding errors may
cause the <code class="docutils literal notranslate"><span class="pre">HSL</span></code> values to differ slightly from what you might expect.</p>
</dd></dl>

<dl class="py attribute definition">
<dt class="sig sig-object py title" id="pygame.Color.i1i2i3">
<span class="sig-name descname"><span class="pre">i1i2i3</span></span><a class="headerlink" href="#pygame.Color.i1i2i3" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets or sets the I1I2I3 representation of the Color.</span></div>
<div class="line"><span class="signature">i1i2i3 -&gt; tuple</span></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">I1I2I3</span></code> representation of the Color. The <code class="docutils literal notranslate"><span class="pre">I1I2I3</span></code> components are
in the ranges <code class="docutils literal notranslate"><span class="pre">I1</span></code> = [0, 1], <code class="docutils literal notranslate"><span class="pre">I2</span></code> = [-0.5, 0.5], <code class="docutils literal notranslate"><span class="pre">I3</span></code> = [-0.5,
0.5]. Note that this will not return the absolutely exact <code class="docutils literal notranslate"><span class="pre">I1I2I3</span></code>
values for the set <code class="docutils literal notranslate"><span class="pre">RGB</span></code> values in all cases. Due to the <code class="docutils literal notranslate"><span class="pre">RGB</span></code>
mapping from 0-255 and the <code class="docutils literal notranslate"><span class="pre">I1I2I3</span></code> mapping from 0-1 rounding errors
may cause the <code class="docutils literal notranslate"><span class="pre">I1I2I3</span></code> values to differ slightly from what you might
expect.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.normalize">
<span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.normalize" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Returns the normalized RGBA values of the Color.</span></div>
<div class="line"><span class="signature">normalize() -&gt; tuple</span></div>
</div>
<p>Returns the normalized <code class="docutils literal notranslate"><span class="pre">RGBA</span></code> values of the Color as floating point
values.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.correct_gamma">
<span class="sig-name descname"><span class="pre">correct_gamma</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.correct_gamma" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Applies a certain gamma value to the Color.</span></div>
<div class="line"><span class="signature">correct_gamma (gamma) -&gt; Color</span></div>
</div>
<p>Applies a certain gamma value to the Color and returns a new Color with
the adjusted <code class="docutils literal notranslate"><span class="pre">RGBA</span></code> values.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.set_length">
<span class="sig-name descname"><span class="pre">set_length</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.set_length" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Set the number of elements in the Color to 1,2,3, or 4.</span></div>
<div class="line"><span class="signature">set_length(len) -&gt; None</span></div>
</div>
<p>DEPRECATED: You may unpack the values you need like so,
<code class="docutils literal notranslate"><span class="pre">r,</span> <span class="pre">g,</span> <span class="pre">b,</span> <span class="pre">_</span> <span class="pre">=</span> <span class="pre">pygame.Color(100,</span> <span class="pre">100,</span> <span class="pre">100)</span></code>
If you only want r, g and b
Or
<code class="docutils literal notranslate"><span class="pre">r,</span> <span class="pre">g,</span> <span class="pre">*_</span> <span class="pre">=</span> <span class="pre">pygame.Color(100,</span> <span class="pre">100,</span> <span class="pre">100)</span></code>
if you only want r and g</p>
<p>The default Color length is 4. Colors can have lengths 1,2,3 or 4. This
is useful if you want to unpack to r,g,b and not r,g,b,a. If you want to
get the length of a Color do <code class="docutils literal notranslate"><span class="pre">len(acolor)</span></code>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since pygame 2.1.3.</span></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.9.0.</span></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.grayscale">
<span class="sig-name descname"><span class="pre">grayscale</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.grayscale" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">returns the grayscale of a Color</span></div>
<div class="line"><span class="signature">grayscale() -&gt; Color</span></div>
</div>
<p>Returns a Color which represents the grayscaled version of self using the luminosity formula which weights red, green and blue according to their wavelengths..</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.lerp">
<span class="sig-name descname"><span class="pre">lerp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.lerp" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">returns a linear interpolation to the given Color.</span></div>
<div class="line"><span class="signature">lerp(Color, float) -&gt; Color</span></div>
</div>
<p>Returns a Color which is a linear interpolation between self and the
given Color in RGBA space. The second parameter determines how far
between self and other the result is going to be.
It must be a value between 0 and 1 where 0 means self and 1 means
other will be returned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.1.</span></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.premul_alpha">
<span class="sig-name descname"><span class="pre">premul_alpha</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.premul_alpha" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">returns a Color where the r,g,b components have been multiplied by the alpha.</span></div>
<div class="line"><span class="signature">premul_alpha() -&gt; Color</span></div>
</div>
<p>Returns a new Color where each of the red, green and blue colour
channels have been multiplied by the alpha channel of the original
color. The alpha channel remains unchanged.</p>
<p>This is useful when working with the <code class="docutils literal notranslate"><span class="pre">BLEND_PREMULTIPLIED</span></code> blending mode
flag for <a class="tooltip reference internal" href="surface.html#pygame.Surface.blit" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Surface.blit()</span></code><span class="tooltip-content">draw one image onto another</span></a>, which assumes that all surfaces using
it are using pre-multiplied alpha colors.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.0.</span></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.Color.update">
<span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.Color.update" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Sets the elements of the color</span></div>
<div class="line"><span class="signature">update(r, g, b) -&gt; None</span></div>
<div class="line"><span class="signature">update(r, g, b, a=255) -&gt; None</span></div>
<div class="line"><span class="signature">update(color_value) -&gt; None</span></div>
</div>
<p>Sets the elements of the color. See parameters for <a class="tooltip reference internal" href="#pygame.Color" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.Color()</span></code><span class="tooltip-content">pygame object for color representations</span></a> for the
parameters of this function. If the alpha value was not set it will not change.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.1.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref/color.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="color_list.html" title="Named Colors"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cdrom.html" title="pygame.cdrom"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Color</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>