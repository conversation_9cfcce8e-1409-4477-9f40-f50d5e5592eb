.. include:: common.txt

:mod:`pygame.surfarray`
=======================

.. module:: pygame.surfarray
   :synopsis: pygame module for accessing surface pixel data using array interfaces

| :sl:`pygame module for accessing surface pixel data using array interfaces`

Functions to convert between NumPy arrays and Surface objects. This module
will only be functional when pygame can use the external NumPy package.
If NumPy can't be imported, ``surfarray`` becomes a ``MissingModule`` object.

Every pixel is stored as a single integer value to represent the red, green,
and blue colors. The 8-bit images use a value that looks into a colormap. Pixels
with higher depth use a bit packing process to place three or four values into
a single number.

The arrays are indexed by the ``X`` axis first, followed by the ``Y`` axis.
Arrays that treat the pixels as a single integer are referred to as 2D arrays.
This module can also separate the red, green, and blue color values into
separate indices. These types of arrays are referred to as 3D arrays, and the
last index is 0 for red, 1 for green, and 2 for blue.

The pixels of a 2D array as returned by :func:`array2d` and :func:`pixels2d`
are mapped to the specific surface. Use :meth:`pygame.Surface.unmap_rgb`
to convert to a color, and :meth:`pygame.Surface.map_rgb` to get the surface
specific pixel value of a color. Integer pixel values can only be used directly
between surfaces with matching pixel layouts (see :class:`pygame.Surface`).

All functions that refer to "array" will copy the surface information to a new
numpy array. All functions that refer to "pixels" will directly reference the
pixels from the surface and any changes performed to the array will make changes
in the surface. As this last functions share memory with the surface, this one
will be locked during the lifetime of the array.

.. function:: array2d

   | :sl:`Copy pixels into a 2d array`
   | :sg:`array2d(Surface) -> array`

   Copy the :meth:`mapped <pygame.Surface.map_rgb>` (raw) pixels from a Surface
   into a 2D array.
   The bit depth of the surface will control the size of the integer values,
   and will work for any type of pixel format.

   This function will temporarily lock the Surface as pixels are copied
   (see the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. ## pygame.surfarray.array2d ##

.. function:: pixels2d

   | :sl:`Reference pixels into a 2d array`
   | :sg:`pixels2d(Surface) -> array`

   Create a new 2D array that directly references the pixel values in a
   Surface. Any changes to the array will affect the pixels in the Surface.
   This is a fast operation since no data is copied.

   Pixels from a 24-bit Surface cannot be referenced, but all other Surface bit
   depths can.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels2d ##

.. function:: array3d

   | :sl:`Copy pixels into a 3d array`
   | :sg:`array3d(Surface) -> array`

   Copy the pixels from a Surface into a 3D array. The bit depth of the surface
   will control the size of the integer values, and will work for any type of
   pixel format.

   This function will temporarily lock the Surface as pixels are copied (see
   the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. ## pygame.surfarray.array3d ##

.. function:: pixels3d

   | :sl:`Reference pixels into a 3d array`
   | :sg:`pixels3d(Surface) -> array`

   Create a new 3D array that directly references the pixel values in a
   Surface. Any changes to the array will affect the pixels in the Surface.
   This is a fast operation since no data is copied.

   This will only work on Surfaces that have 24-bit or 32-bit formats. Lower
   pixel formats cannot be referenced.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels3d ##

.. function:: array_alpha

   | :sl:`Copy pixel alphas into a 2d array`
   | :sg:`array_alpha(Surface) -> array`

   Copy the pixel alpha values (degree of transparency) from a Surface into a
   2D array. This will work for any type of Surface format. Surfaces without a
   pixel alpha will return an array with all opaque values.

   This function will temporarily lock the Surface as pixels are copied (see
   the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. ## pygame.surfarray.array_alpha ##

.. function:: pixels_alpha

   | :sl:`Reference pixel alphas into a 2d array`
   | :sg:`pixels_alpha(Surface) -> array`

   Create a new 2D array that directly references the alpha values (degree of
   transparency) in a Surface. Any changes to the array will affect the pixels
   in the Surface. This is a fast operation since no data is copied.

   This can only work on 32-bit Surfaces with a per-pixel alpha value.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels_alpha ##

.. function:: array_red

   | :sl:`Copy red pixels into a 2d array`
   | :sg:`array_red(Surface) -> array`

   Copy the pixel red values from a Surface into a 2D array. This will work
   for any type of Surface format.

   This function will temporarily lock the Surface as pixels are copied (see
   the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. versionadded:: 2.0.2

   .. ## pygame.surfarray.array_red ##

.. function:: pixels_red

   | :sl:`Reference pixel red into a 2d array.`
   | :sg:`pixels_red (Surface) -> array`

   Create a new 2D array that directly references the red values in a Surface.
   Any changes to the array will affect the pixels in the Surface. This is a
   fast operation since no data is copied.

   This can only work on 24-bit or 32-bit Surfaces.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels_red ##

.. function:: array_green

   | :sl:`Copy green pixels into a 2d array`
   | :sg:`array_green(Surface) -> array`

   Copy the pixel green values from a Surface into a 2D array. This will work
   for any type of Surface format.

   This function will temporarily lock the Surface as pixels are copied (see
   the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. versionadded:: 2.0.2

   .. ## pygame.surfarray.array_green ##

.. function:: pixels_green

   | :sl:`Reference pixel green into a 2d array.`
   | :sg:`pixels_green (Surface) -> array`

   Create a new 2D array that directly references the green values in a
   Surface. Any changes to the array will affect the pixels in the Surface.
   This is a fast operation since no data is copied.

   This can only work on 24-bit or 32-bit Surfaces.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels_green ##

.. function:: array_blue

   | :sl:`Copy blue pixels into a 2d array`
   | :sg:`array_blue(Surface) -> array`

   Copy the pixel blue values from a Surface into a 2D array. This will work
   for any type of Surface format.

   This function will temporarily lock the Surface as pixels are copied (see
   the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method).

   .. versionadded:: 2.0.2

   .. ## pygame.surfarray.array_blue ##

.. function:: pixels_blue

   | :sl:`Reference pixel blue into a 2d array.`
   | :sg:`pixels_blue (Surface) -> array`

   Create a new 2D array that directly references the blue values in a Surface.
   Any changes to the array will affect the pixels in the Surface. This is a
   fast operation since no data is copied.

   This can only work on 24-bit or 32-bit Surfaces.

   The Surface this references will remain locked for the lifetime of the array,
   since the array generated by this function shares memory with the surface.
   See the :meth:`pygame.Surface.lock` - lock the Surface memory for pixel
   access method.

   .. ## pygame.surfarray.pixels_blue ##

.. function:: array_colorkey

   | :sl:`Copy the colorkey values into a 2d array`
   | :sg:`array_colorkey(Surface) -> array`

   Create a new array with the colorkey transparency value from each pixel. If
   the pixel matches the colorkey it will be fully transparent; otherwise it
   will be fully opaque.

   This will work on any type of Surface format. If the image has no colorkey a
   solid opaque array will be returned.

   This function will temporarily lock the Surface as pixels are copied.

   .. ## pygame.surfarray.array_colorkey ##

.. function:: make_surface

   | :sl:`Copy an array to a new surface`
   | :sg:`make_surface(array) -> Surface`

   Create a new Surface that best resembles the data and format on the array.
   The array can be 2D or 3D with any sized integer values. Function
   make_surface uses the array struct interface to acquire array properties,
   so is not limited to just NumPy arrays. See :mod:`pygame.pixelcopy`.

   New in pygame 1.9.2: array struct interface support.

   .. ## pygame.surfarray.make_surface ##

.. function:: blit_array

   | :sl:`Blit directly from a array values`
   | :sg:`blit_array(Surface, array) -> None`

   Directly copy values from an array into a Surface. This is faster than
   converting the array into a Surface and blitting. The array must be the same
   dimensions as the Surface and will completely replace all pixel values. Only
   integer, ASCII character and record arrays are accepted.

   This function will temporarily lock the Surface as the new values are
   copied.

   .. ## pygame.surfarray.blit_array ##

.. function:: map_array

   | :sl:`Map a 3d array into a 2d array`
   | :sg:`map_array(Surface, array3d) -> array2d`

   Convert a 3D array into a 2D array. This will use the given Surface format
   to control the conversion. Palette surface formats are supported for NumPy
   arrays.

   .. ## pygame.surfarray.map_array ##

.. function:: use_arraytype

   | :sl:`Sets the array system to be used for surface arrays`
   | :sg:`use_arraytype (arraytype) -> None`

   DEPRECATED: Uses the requested array type for the module functions.
   The only supported arraytype is ``'numpy'``. Other values will raise
   ValueError. Using this function will raise a ``DeprecationWarning``.

   .. ## pygame.surfarray.use_arraytype ##

.. function:: get_arraytype

   | :sl:`Gets the currently active array type.`
   | :sg:`get_arraytype () -> str`

   DEPRECATED: Returns the currently active array type. This will be a value of the
   ``get_arraytypes()`` tuple and indicates which type of array module is used
   for the array creation. Using this function will raise a ``DeprecationWarning``.

   .. versionadded:: 1.8

   .. ## pygame.surfarray.get_arraytype ##

.. function:: get_arraytypes

   | :sl:`Gets the array system types currently supported.`
   | :sg:`get_arraytypes () -> tuple`

   DEPRECATED: Checks, which array systems are available and returns them as a tuple of
   strings. The values of the tuple can be used directly in the
   :func:`pygame.surfarray.use_arraytype` () method. If no supported array
   system could be found, None will be returned.  Using this function will raise a
   ``DeprecationWarning``.

   .. versionadded:: 1.8

   .. ## pygame.surfarray.get_arraytypes ##

.. ## pygame.surfarray ##
