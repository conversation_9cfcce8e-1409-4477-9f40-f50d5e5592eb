# iOS Project Setup Instructions

The Xcode project file had some formatting issues. Here's how to create a working iOS project:

## Option 1: Create New Xcode Project (Recommended)

1. **Open Xcode**
2. **Create a new project:**
   - Choose "iOS" → "Game"
   - Product Name: `TurnMeOnGame`
   - Interface: `Storyboard`
   - Language: `Swift`
   - Game Technology: `SpriteKit`
   - Use Core Data: `No`

3. **Replace the generated files with our converted files:**
   - Replace `AppDelegate.swift` with our version
   - Replace `GameViewController.swift` with our version
   - Replace `GameScene.swift` with our version
   - Add `GameObjects.swift`
   - Add `GameState.swift`
   - Add `AudioManager.swift`

4. **Add Assets:**
   - Copy the `Assets.xcassets` folder contents
   - Copy the sound files to the project
   - Copy the storyboard files

5. **Update Info.plist:**
   - Copy our Info.plist settings

## Option 2: Use Swift Package (Alternative)

1. **Open Xcode**
2. **File → Open** and select the `Package.swift` file
3. This will open the project as a Swift Package
4. You can then create an iOS app target that uses this package

## Option 3: Manual File Import

If you have an existing iOS project:

1. **Drag and drop all Swift files** from `TurnMeOnGame/` into your Xcode project
2. **Add the assets** to your project's asset catalog
3. **Copy the sound files** to your project bundle
4. **Update your Info.plist** with our settings

## Files to Import

### Swift Source Files:
- `AppDelegate.swift` - App lifecycle management
- `GameViewController.swift` - Main view controller
- `GameScene.swift` - Core game logic and rendering
- `GameObjects.swift` - Game sprite classes
- `GameState.swift` - Game state and persistence
- `AudioManager.swift` - Sound effect management

### Resources:
- `Assets.xcassets/` - Image assets (bulb, star, gremlin)
- `Sounds/` - Audio files (WAV format)
- `Base.lproj/` - Storyboard files
- `GameScene.sks` - SpriteKit scene file
- `Actions.sks` - SpriteKit actions file

### Configuration:
- `Info.plist` - App configuration and permissions

## Key Project Settings

Make sure your project has these settings:

### Deployment Info:
- **Deployment Target:** iOS 17.0+
- **Device Orientation:** Portrait only (iPhone), Portrait + Landscape (iPad)
- **Status Bar:** Hidden during gameplay

### Capabilities:
- No special capabilities required
- Audio session configured for games

### Build Settings:
- **Swift Language Version:** Swift 5
- **Code Signing:** Automatic (for development)

## Testing the Game

Once set up:

1. **Build and run** on iOS Simulator or device
2. **Test touch controls** - tap lightbulbs, stars, gremlins
3. **Test audio** - ensure sound effects play
4. **Test difficulty levels** - try different game modes
5. **Test persistence** - high scores should save between sessions

## Troubleshooting

### Common Issues:

1. **Build Errors:**
   - Check that all Swift files are added to the target
   - Verify iOS deployment target is 17.0+
   - Ensure SpriteKit framework is linked

2. **Missing Assets:**
   - Verify images are in Assets.xcassets
   - Check sound files are in the bundle
   - Confirm file names match code references

3. **Runtime Issues:**
   - Check device/simulator iOS version
   - Verify audio session permissions
   - Test on actual device for best performance

### File Structure Should Look Like:
```
TurnMeOnGame/
├── AppDelegate.swift
├── GameViewController.swift
├── GameScene.swift
├── GameObjects.swift
├── GameState.swift
├── AudioManager.swift
├── Assets.xcassets/
│   ├── AppIcon.appiconset/
│   ├── bulb_off.imageset/
│   ├── bulb_on.imageset/
│   ├── star.imageset/
│   └── gremlin.imageset/
├── Sounds/
│   ├── bulb_click.wav
│   ├── bulb_break.wav
│   ├── gremlin_hit.wav
│   └── star_collect.wav
├── Base.lproj/
│   ├── Main.storyboard
│   └── LaunchScreen.storyboard
├── GameScene.sks
├── Actions.sks
└── Info.plist
```

## Next Steps

After setting up the project:

1. **Customize the game** - modify difficulty settings, colors, etc.
2. **Add app icons** - create proper app icons for the App Store
3. **Test thoroughly** - on different devices and iOS versions
4. **Consider additional features** - achievements, leaderboards, etc.

The game is fully functional and ready to play once properly set up in Xcode!
