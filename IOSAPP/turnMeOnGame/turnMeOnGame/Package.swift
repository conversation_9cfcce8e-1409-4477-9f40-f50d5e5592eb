// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "TurnMeOnGame",
    platforms: [
        .iOS(.v17)
    ],
    products: [
        .library(
            name: "TurnMeOnGame",
            targets: ["TurnMeOnGame"]
        ),
    ],
    dependencies: [
    ],
    targets: [
        .target(
            name: "TurnMeOnGame",
            dependencies: [],
            path: "TurnMeOnGame",
            resources: [
                .process("Assets.xcassets"),
                .process("Base.lproj"),
                .process("Sounds"),
                .process("GameScene.sks"),
                .process("Actions.sks")
            ]
        ),
    ]
)
