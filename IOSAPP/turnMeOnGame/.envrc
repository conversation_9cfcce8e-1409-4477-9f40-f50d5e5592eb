# Environment variables for turnMeOnGame project
# This file is managed by direnv - run 'direnv allow' to activate

# Development environment
export NODE_ENV=development

# Project root
export PROJECT_ROOT=$(pwd)

# Common paths
export PATH="$PROJECT_ROOT/bin:$PATH"
export PATH="$PROJECT_ROOT/node_modules/.bin:$PATH"

# Game-specific environment variables
export GAME_DEBUG=true
export GAME_LOG_LEVEL=debug

# Database (if using one)
# export DATABASE_URL="sqlite://./game.db"

# Port for local development server
export PORT=3000

# Add any other project-specific environment variables below
