<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame C API &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/pygame.css?v=a854c6a8" />
    <script src="_static/documentation_options.js?v=0a414f3d"></script>
    <script src="_static/doctools.js?v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="_static/pygame.ico"/>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Slots and c_api - Making functions and data available from other modules" href="c_api/slots.html" />
    <link rel="prev" title="한국어 튜토리얼" href="tut/ko/%EB%B9%A8%EA%B0%84%EB%B8%94%EB%A1%9D%20%EA%B2%80%EC%9D%80%EB%B8%94%EB%A1%9D/%EA%B0%9C%EC%9A%94.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="index.html">Help Contents</a> ||
	    <a href="genindex.html">Reference Index</a>

        <form action="search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="ref/color.html">Color</a> | 
	    <a href="ref/display.html">display</a> | 
	    <a href="ref/draw.html">draw</a> | 
	    <a href="ref/event.html">event</a> | 
	    <a href="ref/font.html">font</a> | 
	    <a href="ref/image.html">image</a> | 
	    <a href="ref/key.html">key</a> | 
	    <a href="ref/locals.html">locals</a> | 
	    <a href="ref/mixer.html">mixer</a> | 
	    <a href="ref/mouse.html">mouse</a> | 
	    <a href="ref/rect.html">Rect</a> | 
	    <a href="ref/surface.html">Surface</a> | 
	    <a href="ref/time.html">time</a> | 
	    <a href="ref/music.html">music</a> | 
	    <a href="ref/pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="ref/cursors.html">cursors</a> | 
	    <a href="ref/joystick.html">joystick</a> | 
	    <a href="ref/mask.html">mask</a> | 
	    <a href="ref/sprite.html">sprite</a> | 
	    <a href="ref/transform.html">transform</a> | 
	    <a href="ref/bufferproxy.html">BufferProxy</a> | 
	    <a href="ref/freetype.html">freetype</a> | 
	    <a href="ref/gfxdraw.html">gfxdraw</a> | 
	    <a href="ref/midi.html">midi</a> | 
	    <a href="ref/pixelarray.html">PixelArray</a> | 
	    <a href="ref/pixelcopy.html">pixelcopy</a> | 
	    <a href="ref/sndarray.html">sndarray</a> | 
	    <a href="ref/surfarray.html">surfarray</a> | 
	    <a href="ref/math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="ref/camera.html">camera</a> | 
	    <a href="ref/sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="ref/examples.html">examples</a> | 
	    <a href="ref/fastevent.html">fastevent</a> | 
	    <a href="ref/scrap.html">scrap</a> | 
	    <a href="ref/tests.html">tests</a> | 
	    <a href="ref/touch.html">touch</a> | 
	    <a href="ref/pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="pygame-c-api">
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="c_api/slots.html">Slots and c_api - Making functions and data available from other modules</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/base.html">High level API exported by pygame.base</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/bufferproxy.html">Class BufferProxy API exported by pygame.bufferproxy</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/color.html">Class Color API exported by pygame.color</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/display.html">API exported by pygame.display</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/event.html">API exported by pygame.event</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/freetype.html">API exported by pygame._freetype</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/mixer.html">API exported by pygame.mixer</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/rect.html">Class Rect API exported by pygame.rect</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/rwobject.html">API exported by pygame.rwobject</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/surface.html">Class Surface API exported by pygame.surface</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/surflock.html">API exported by pygame.surflock</a></li>
<li class="toctree-l1"><a class="reference internal" href="c_api/version.html">API exported by pygame.version</a></li>
</ul>
</div>
<p>src_c/include/ contains header files for applications
that use the pygame C API, while src_c/ contains
headers used by pygame internally.</p>
</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/c_api.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="c_api/slots.html" title="Slots and c_api - Making functions and data available from other modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tut/ko/%EB%B9%A8%EA%B0%84%EB%B8%94%EB%A1%9D%20%EA%B2%80%EC%9D%80%EB%B8%94%EB%A1%9D/%EA%B0%9C%EC%9A%94.html" title="한국어 튜토리얼"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">pygame C API</a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>