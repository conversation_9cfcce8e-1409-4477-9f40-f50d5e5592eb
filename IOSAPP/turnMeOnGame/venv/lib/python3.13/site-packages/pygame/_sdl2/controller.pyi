from typing import Dict, Mapping, Optional

from pygame.joystick import JoystickType

def init() -> None: ...
def get_init() -> bool: ...
def quit() -> None: ...
def set_eventstate(state: bool) -> None: ...
def get_eventstate() -> bool: ...
def update() -> None: ...
def get_count() -> int: ...
def is_controller(index: int) -> bool: ...
def name_forindex(index: int) -> Optional[str]: ...

class Controller:
    def __init__(self, index: int) -> None: ...
    @property
    def name(self) -> str: ...
    @property
    def id(self) -> int: ...
    def init(self) -> None: ...
    def get_init(self) -> bool: ...
    def quit(self) -> None: ...
    @staticmethod
    def from_joystick(joy: JoystickType) -> Controller: ...
    def attached(self) -> bool: ...
    def as_joystick(self) -> JoystickType: ...
    def get_axis(self, axis: int) -> int: ...
    def get_button(self, button: int) -> bool: ...
    def get_mapping(self) -> Dict[str, str]: ...
    def set_mapping(self, mapping: Mapping[str, str]) -> int: ...
    def rumble(
        self, low_frequency: float, high_frequency: float, duration: int
    ) -> bool: ...
    def stop_rumble(self) -> None: ...
