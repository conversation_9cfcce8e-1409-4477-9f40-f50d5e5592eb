These examples are a good introduction to various Pygame modules and 
techniques. They are beginner-friendly with source code in the public 
domain that can be adapted for your projects.


aacircles.py
   An example of using the gfxdraw module to drawing anti-aliased circles.

aliens.py
   An arcade-style space shooter game that showcases various common and 
   important Pygame modules and techniques.

arraydemo.py
   Showcases the use of Numpy with Pygame to perform efficient 
   pixel manipulation.

audiocapture.py
   Use the mixer module to record sound from a microphone, and 
   play back the recorded sound.

blend_fill.py
   Demonstrates how to perform surface blending and filling 
   with Pygame.

blit_blends.py
   Uses blit functions to showcase some of Pygame's different 
   blending modes.

camera.py
   Basic image capturing and display using pygame.camera

chimp.py
   A simple game featuring a chimp that showcases the use of 
   common and important Pygame modules and techniques.

cursors.py
   Demonstrates the creation of custom cursors with Pygame.

dropevent.py
   Drag and drop files using the following events:
   DROPBEGIN, DROPCOMPLETE, DROPTEXT, DROPFILE

eventlist.py
   A utility for displaying and logging real-time Pygame events, 
   useful for debugging.

font_viewer.py
   Demonstrates how to display all available fonts in a 
   scrolling window.

fonty.py
   A simple application demonstrating the different ways 
   to render fonts with the font module

freetype_misc.py
   Shows how to use the freetype module to perform font
   rendering and manipulation.

glcube.py
   Using PyOpenGL and Pygame, this creates a spinning 3D multicolored cube.

go_over_there.py
   Demonstrates the important Vector.move_towards() function.

grid.py
   A simple example of grid-based movement.

headless_no_windows_needed.py
   Shows how to run Pygame in scripts.

joystick.py
   Shows how to integrate joysticks or game controllers into Pygame.

liquid.py
   Demonstrates how to create a simple liquid effect in an image. 

mask.py
   Showcases how to use masks for collision detection and sprite 
   interaction.

midi.py
   Demonstrates how to use MIDI I/O using the midi module.

moveit.py
   Illustrates how to accomplish sprite movement and animation.

music_drop_fade.py
   Showcases dropping music files into Pygame, and how to
   apply a fade effect to music playback.

pixelarray.py
   Manipulation of individual pixels using the PixelArray module.

playmus.py
   Uses the mixer module to play music files with CLI.

prevent_display_stretching.py
   Illustrates how to maintain aspect ratio when resizing a window
   in Pygame.

resizing_new.py
   Showcases various window resizing events and how to fit graphics
   to new dimensions.

scaletest.py
   Showcases the scaling of Surfaces.

scrap_clipboard.py
   Shows how to implement clipboard interaction with Pygame's scrap module.

scroll.py
   An example that implements smooth scrolling backgrounds for side-scrolling 
   games or parallax effects.

setmodescale.py
   Handles mouse scaling and selection of a good sized window depending
   on the display.

sound.py
   Illustrates how to implement sound effects and music using Pygame.

sound_array_demos.py
   Showcases echo, delay and other array based processing of sounds.

sprite_texture.py
   Demonstrates how to use textured sprites in Pygame.

stars.py
   A simple starfield implementation in which the perspective can be
   changed by a mouse click.

testsprite.py
   Showcases the basics of sprite handling, namely collision 
   detection and animation.

textinput.py
   A little "console" where you can write in text.
   Shows how to use the TEXTEDITING and TEXTINPUT events.

vgrade.py
   Shows how to apply vertical gradients to surfaces using Pygame.

video.py
   Showcases the movie module, including the display of playback 
   controls.

data/
   Directory with the resources for the examples.


More examples can be found on the Pygame website and GitHub.
We're always looking for new examples and/or example requests. Examining
code such as this is a great way to get started with Python-based 
game development.
