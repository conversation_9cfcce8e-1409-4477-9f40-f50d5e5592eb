# Turn Me On - iOS Game

A fast-paced iOS game where you must keep the lights on by clicking lightbulbs, collecting stars for points, and avoiding mischievous gremlins!

## Game Overview

**Turn Me On** is an action-packed mobile game converted from Python/Pygame to native iOS using Swift and SpriteKit. The objective is simple: keep your brightness level above zero by lighting up falling lightbulbs while collecting stars and avoiding gremlins.

## Features

### Core Gameplay
- **Lightbulbs**: Tap falling lightbulbs to turn them on and increase brightness
- **Stars**: Collect blue stars for points and combo bonuses
- **Gremlins**: Avoid purple gremlins that steal your bulbs and stars
- **Power-ups**: Collect special power-ups for temporary advantages
- **Overcharge System**: Light multiple bulbs to enter overcharge mode for bonus effects

### iOS-Specific Features
- **Touch Controls**: Optimized touch handling with visual feedback
- **Haptic Feedback**: Tactile responses for different game actions
- **Portrait Orientation**: Designed for one-handed mobile play
- **Audio Management**: Proper iOS audio session handling with interruption support
- **Persistence**: High scores and settings saved using UserDefaults
- **Performance Optimized**: Smooth 60fps gameplay on iOS devices

### Difficulty Levels
- **Easy**: Fewer gremlins, slower pace, perfect for beginners
- **Okay**: Balanced challenge for regular players
- **Mad Man**: Insane gremlin chaos for hardcore players

### Power-ups
- **Freeze**: Temporarily freezes all gremlins
- **Shield**: Protects against brightness loss
- **Boost**: Instant brightness restoration
- **Multi**: Lights up all bulbs on screen

## Technical Implementation

### Architecture
- **SpriteKit**: Core game engine for 2D graphics and physics
- **Swift**: Native iOS development language
- **AVAudioEngine**: Audio system with proper iOS session management
- **UserDefaults**: Persistent storage for scores and settings

### Key Classes
- `GameScene`: Main game logic and rendering
- `GameState`: Game state management and persistence
- `GameObjects`: Sprite classes (Lightbulb, Star, Gremlin, etc.)
- `AudioManager`: Sound effect management
- `GameViewController`: iOS view controller integration

### Performance Features
- Efficient sprite pooling and removal
- Optimized collision detection
- Frame-rate independent game logic
- Memory-conscious particle systems

## Installation & Setup

### Requirements
- Xcode 15.0 or later
- iOS 17.0 or later
- iPhone/iPad device or simulator

### Building the Project
1. Open `TurnMeOnGame.xcodeproj` in Xcode
2. Select your target device or simulator
3. Build and run the project (⌘+R)

### Assets
The project includes all necessary game assets:
- **Images**: Lightbulb sprites, star, gremlin graphics
- **Sounds**: Click, break, hit, and collect sound effects
- **Icons**: App icon placeholders (customize as needed)

## Game Controls

### Touch Interactions
- **Tap**: Interact with game objects (bulbs, stars, gremlins, power-ups)
- **Long Press**: Pause the game (1 second hold)
- **Menu Navigation**: Tap to navigate through menus

### Visual Feedback
- Touch indicators show where you've tapped
- Success effects highlight successful interactions
- Screen shake for impactful events
- Glow effects for lightbulb activation

## Customization

### Difficulty Tuning
Modify `DifficultySettings` in `GameState.swift`:
```swift
static func settings(for difficulty: Difficulty) -> DifficultySettings {
    // Adjust spawn rates, gremlin counts, etc.
}
```

### Audio Settings
Configure audio in `AudioManager.swift`:
```swift
private func setupAudioSession() {
    // Modify audio session category and options
}
```

### Visual Customization
Update game constants in `GameConstants.swift`:
```swift
struct GameConstants {
    static let screenWidth: CGFloat = 480
    static let screenHeight: CGFloat = 800
    // Modify colors, sizes, etc.
}
```

## Future Enhancements

### Potential Features
- **Achievements System**: Track player accomplishments
- **Leaderboards**: Global score comparison via Game Center
- **Additional Power-ups**: More special abilities
- **Boss Battles**: Special challenging encounters
- **Themes**: Different visual styles and backgrounds
- **Sound Settings**: Music tracks and volume controls

### Technical Improvements
- **Core Data**: More sophisticated data persistence
- **CloudKit**: Cross-device save synchronization
- **Metal**: Advanced graphics rendering
- **Multiplayer**: Real-time competitive gameplay

## Troubleshooting

### Common Issues
1. **Audio Not Playing**: Check device volume and silent mode
2. **Performance Issues**: Test on actual device rather than simulator
3. **Touch Not Responsive**: Ensure single-touch mode is enabled
4. **Orientation Problems**: Verify Info.plist orientation settings

### Debug Features
- Enable FPS display in `GameViewController.swift`
- Add debug logging in `GameScene.swift`
- Monitor memory usage in Xcode Instruments

## Credits

**Original Python Version**: Converted from Pygame implementation
**iOS Conversion**: Native Swift/SpriteKit adaptation
**Assets**: Game graphics and sound effects included
**Framework**: Built with Apple's SpriteKit game engine

## License

This project is provided as-is for educational and development purposes. Customize and distribute according to your needs.
