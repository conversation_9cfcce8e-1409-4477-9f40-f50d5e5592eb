// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A2B3C4D5E6F7890ABCD /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCE /* AppDelegate.swift */; };
		1A2B3C4D5E6F7890ABCF /* GameScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD0 /* GameScene.sks */; };
		1A2B3C4D5E6F7890ABD1 /* Actions.sks in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD2 /* Actions.sks */; };
		1A2B3C4D5E6F7890ABD3 /* GameScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD4 /* GameScene.swift */; };
		1A2B3C4D5E6F7890ABD5 /* GameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD6 /* GameViewController.swift */; };
		1A2B3C4D5E6F7890ABD7 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD8 /* Main.storyboard */; };
		1A2B3C4D5E6F7890ABD9 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDA /* Assets.xcassets */; };
		1A2B3C4D5E6F7890ABDB /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDC /* LaunchScreen.storyboard */; };
		1A2B3C4D5E6F7890ABDE /* GameObjects.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDD /* GameObjects.swift */; };
		1A2B3C4D5E6F7890ABE0 /* AudioManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDF /* AudioManager.swift */; };
		1A2B3C4D5E6F7890ABE2 /* GameState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE1 /* GameState.swift */; };
		1A2B3C4D5E6F7890ABE4 /* bulb_click.wav in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE3 /* bulb_click.wav */; };
		1A2B3C4D5E6F7890ABE6 /* bulb_break.wav in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE5 /* bulb_break.wav */; };
		1A2B3C4D5E6F7890ABE8 /* gremlin_hit.wav in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE7 /* gremlin_hit.wav */; };
		1A2B3C4D5E6F7890ABEA /* star_collect.wav in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE9 /* star_collect.wav */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A2B3C4D5E6F7890ABCB /* TurnMeOnGame.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TurnMeOnGame.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A2B3C4D5E6F7890ABCE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD0 /* GameScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = GameScene.sks; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD2 /* Actions.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = Actions.sks; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD4 /* GameScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameScene.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD6 /* GameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameViewController.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD9 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDA /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDD /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDD /* GameObjects.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameObjects.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDF /* AudioManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioManager.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE1 /* GameState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameState.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE3 /* bulb_click.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = bulb_click.wav; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE5 /* bulb_break.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = bulb_break.wav; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE7 /* gremlin_hit.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = gremlin_hit.wav; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE9 /* star_collect.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = star_collect.wav; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A2B3C4D5E6F7890ABC8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A2B3C4D5E6F7890ABC2 = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCD /* TurnMeOnGame */,
				1A2B3C4D5E6F7890ABCC /* Products */,
			);
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABCC /* Products */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCB /* TurnMeOnGame.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABCD /* TurnMeOnGame */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCE /* AppDelegate.swift */,
				1A2B3C4D5E6F7890ABD0 /* GameScene.sks */,
				1A2B3C4D5E6F7890ABD2 /* Actions.sks */,
				1A2B3C4D5E6F7890ABD4 /* GameScene.swift */,
				1A2B3C4D5E6F7890ABDD /* GameObjects.swift */,
				1A2B3C4D5E6F7890ABE1 /* GameState.swift */,
				1A2B3C4D5E6F7890ABDF /* AudioManager.swift */,
				1A2B3C4D5E6F7890ABD6 /* GameViewController.swift */,
				1A2B3C4D5E6F7890ABE3 /* bulb_click.wav */,
				1A2B3C4D5E6F7890ABE5 /* bulb_break.wav */,
				1A2B3C4D5E6F7890ABE7 /* gremlin_hit.wav */,
				1A2B3C4D5E6F7890ABE9 /* star_collect.wav */,
				1A2B3C4D5E6F7890ABD8 /* Main.storyboard */,
				1A2B3C4D5E6F7890ABDA /* Assets.xcassets */,
				1A2B3C4D5E6F7890ABDC /* LaunchScreen.storyboard */,
				1A2B3C4D5E6F7890ABDE /* Info.plist */,
			);
			path = TurnMeOnGame;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A2B3C4D5E6F7890ABCA /* TurnMeOnGame */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A2B3C4D5E6F7890ABE1 /* Build configuration list for PBXNativeTarget "TurnMeOnGame" */;
			buildPhases = (
				1A2B3C4D5E6F7890ABC7 /* Sources */,
				1A2B3C4D5E6F7890ABC8 /* Frameworks */,
				1A2B3C4D5E6F7890ABC9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TurnMeOnGame;
			productName = TurnMeOnGame;
			productReference = 1A2B3C4D5E6F7890ABCB /* TurnMeOnGame.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A2B3C4D5E6F7890ABC3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A2B3C4D5E6F7890ABCA = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A2B3C4D5E6F7890ABC6 /* Build configuration list for PBXProject "TurnMeOnGame" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A2B3C4D5E6F7890ABC2;
			productRefGroup = 1A2B3C4D5E6F7890ABCC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A2B3C4D5E6F7890ABCA /* TurnMeOnGame */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A2B3C4D5E6F7890ABC9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7890ABD7 /* Main.storyboard in Resources */,
				1A2B3C4D5E6F7890ABCF /* GameScene.sks in Resources */,
				1A2B3C4D5E6F7890ABDB /* LaunchScreen.storyboard in Resources */,
				1A2B3C4D5E6F7890ABD9 /* Assets.xcassets in Resources */,
				1A2B3C4D5E6F7890ABD1 /* Actions.sks in Resources */,
				1A2B3C4D5E6F7890ABE4 /* bulb_click.wav in Resources */,
				1A2B3C4D5E6F7890ABE6 /* bulb_break.wav in Resources */,
				1A2B3C4D5E6F7890ABE8 /* gremlin_hit.wav in Resources */,
				1A2B3C4D5E6F7890ABEA /* star_collect.wav in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A2B3C4D5E6F7890ABC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7890ABD3 /* GameScene.swift in Sources */,
				1A2B3C4D5E6F7890ABD5 /* GameViewController.swift in Sources */,
				1A2B3C4D5E6F7890ABDE /* GameObjects.swift in Sources */,
				1A2B3C4D5E6F7890ABE0 /* AudioManager.swift in Sources */,
				1A2B3C4D5E6F7890ABE2 /* GameState.swift in Sources */,
				1A2B3C4D5E6F7890ABCD /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		1A2B3C4D5E6F7890ABD8 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1A2B3C4D5E6F7890ABD9 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABDC /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1A2B3C4D5E6F7890ABDD /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1A2B3C4D5E6F7890ABDF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A2B3C4D5E6F7890ABE0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A2B3C4D5E6F7890ABE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TurnMeOnGame/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.TurnMeOnGame;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A2B3C4D5E6F7890ABE3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TurnMeOnGame/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.TurnMeOnGame;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A2B3C4D5E6F7890ABC6 /* Build configuration list for PBXProject "TurnMeOnGame" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F7890ABDF /* Debug */,
				1A2B3C4D5E6F7890ABE0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A2B3C4D5E6F7890ABE1 /* Build configuration list for PBXNativeTarget "TurnMeOnGame" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F7890ABE2 /* Debug */,
				1A2B3C4D5E6F7890ABE3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A2B3C4D5E6F7890ABC3 /* Project object */;
}
