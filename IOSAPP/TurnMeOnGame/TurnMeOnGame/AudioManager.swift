import AVFoundation
import Foundation

class AudioManager {
    static let shared = AudioManager()
    
    private var audioPlayers: [String: AVAudioPlayer] = [:]
    private var soundEnabled: Bool = true
    
    private init() {
        setupAudioSession()
        loadSounds()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func loadSounds() {
        let soundFiles = [
            "bulb_click": "bulb_click.wav",
            "bulb_break": "bulb_break.wav",
            "gremlin_hit": "gremlin_hit.wav",
            "star_collect": "star_collect.wav"
        ]
        
        for (key, filename) in soundFiles {
            if let path = Bundle.main.path(forResource: filename.replacingOccurrences(of: ".wav", with: ""), ofType: "wav") {
                do {
                    let player = try AVAudioPlayer(contentsOf: URL(fileURLWithPath: path))
                    player.prepareToPlay()
                    audioPlayers[key] = player
                } catch {
                    print("Failed to load sound \(filename): \(error)")
                }
            } else {
                print("Sound file not found: \(filename)")
            }
        }
    }
    
    func playSound(_ soundName: String) {
        guard soundEnabled, let player = audioPlayers[soundName] else { return }
        
        player.stop()
        player.currentTime = 0
        player.play()
    }
    
    func setSoundEnabled(_ enabled: Bool) {
        soundEnabled = enabled
    }
    
    func isSoundEnabled() -> Bool {
        return soundEnabled
    }
    
    // Convenience methods for specific sounds
    func playBulbClick() {
        playSound("bulb_click")
    }
    
    func playBulbBreak() {
        playSound("bulb_break")
    }
    
    func playGremlinHit() {
        playSound("gremlin_hit")
    }
    
    func playStarCollect() {
        playSound("star_collect")
    }
}
