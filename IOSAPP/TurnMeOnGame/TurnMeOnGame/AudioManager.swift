import AVFoundation
import Foundation

class AudioManager {
    static let shared = AudioManager()
    
    private var audioPlayers: [String: AVAudioPlayer] = [:]
    private var soundEnabled: Bool = true
    
    private init() {
        setupAudioSession()
        loadSounds()
    }
    
    private func setupAudioSession() {
        do {
            // Set up audio session for games
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .gameChat, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)

            // Handle audio interruptions (calls, etc.)
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioInterruption),
                name: AVAudioSession.interruptionNotification,
                object: nil
            )

        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }

    @objc private func handleAudioInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        switch type {
        case .began:
            // Audio interruption began - pause all sounds
            pauseAllSounds()
        case .ended:
            // Audio interruption ended - can resume if needed
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    resumeAllSounds()
                }
            }
        @unknown default:
            break
        }
    }
    
    private func loadSounds() {
        let soundFiles = [
            "bulb_click": "bulb_click.wav",
            "bulb_break": "bulb_break.wav",
            "gremlin_hit": "gremlin_hit.wav",
            "star_collect": "star_collect.wav"
        ]

        for (key, filename) in soundFiles {
            if let url = Bundle.main.url(forResource: filename.replacingOccurrences(of: ".wav", with: ""), withExtension: "wav") {
                do {
                    let player = try AVAudioPlayer(contentsOf: url)
                    player.prepareToPlay()
                    player.volume = 0.7 // Set default volume
                    audioPlayers[key] = player
                } catch {
                    print("Failed to load sound \(filename): \(error)")
                }
            } else {
                print("Sound file not found: \(filename)")
            }
        }
    }
    
    func playSound(_ soundName: String) {
        guard soundEnabled, let player = audioPlayers[soundName] else { return }
        
        player.stop()
        player.currentTime = 0
        player.play()
    }
    
    func setSoundEnabled(_ enabled: Bool) {
        soundEnabled = enabled
    }
    
    func isSoundEnabled() -> Bool {
        return soundEnabled
    }
    
    // Convenience methods for specific sounds
    func playBulbClick() {
        playSound("bulb_click")
    }
    
    func playBulbBreak() {
        playSound("bulb_break")
    }
    
    func playGremlinHit() {
        playSound("gremlin_hit")
    }
    
    func playStarCollect() {
        playSound("star_collect")
    }

    // MARK: - Audio Control
    func pauseAllSounds() {
        for player in audioPlayers.values {
            if player.isPlaying {
                player.pause()
            }
        }
    }

    func resumeAllSounds() {
        // Note: Individual sounds are typically short, so we don't usually resume them
        // This method is here for completeness and future expansion
    }

    func setMasterVolume(_ volume: Float) {
        let clampedVolume = max(0.0, min(1.0, volume))
        for player in audioPlayers.values {
            player.volume = clampedVolume
        }
    }

    func getMasterVolume() -> Float {
        return audioPlayers.values.first?.volume ?? 0.7
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
