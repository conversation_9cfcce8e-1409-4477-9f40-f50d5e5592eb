import SpriteKit
import GameplayKit

class GameScene: SKScene {
    
    // MARK: - Game State
    private var gameState = GameState()
    private var screenShake = ScreenShake()
    private var lastUpdateTime: TimeInterval = 0
    
    // MARK: - Game Assets
    private var bulbOffTexture: SKTexture!
    private var bulbOnTexture: SKTexture!
    private var starTexture: SKTexture!
    private var gremlinTexture: SKTexture!
    
    // MARK: - Sprite Groups
    private var lightbulbs: [Lightbulb] = []
    private var stars: [Star] = []
    private var gremlins: [Gremlin] = []
    private var powerups: [PowerUp] = []
    private var particles: [Particle] = []
    
    // MARK: - Timers
    private var bulbSpawnTimer: TimeInterval = 0
    private var starSpawnTimer: TimeInterval = 0
    private var gremlinSpawnTimer: TimeInterval = 0
    private var powerupSpawnTimer: TimeInterval = 0
    
    private let bulbSpawnInterval: TimeInterval = 1.0
    private var baseStarInterval: TimeInterval = 2.5
    private var currentStarInterval: TimeInterval = 2.5
    private let powerupSpawnInterval: TimeInterval = 15.0
    
    // MARK: - UI Elements
    private var uiLayer: SKNode!
    private var gameLayer: SKNode!
    private var backgroundLayer: SKNode!
    
    // MARK: - Game States
    private enum GamePhase {
        case startScreen
        case difficultySelection
        case playing
        case paused
        case gameOver
    }
    
    private var currentPhase: GamePhase = .startScreen
    
    override func didMove(to view: SKView) {
        setupScene()
        loadAssets()
        showStartScreen()
    }
    
    // MARK: - Scene Setup
    private func setupScene() {
        backgroundColor = GameConstants.black
        
        // Create layers
        backgroundLayer = SKNode()
        gameLayer = SKNode()
        uiLayer = SKNode()
        
        addChild(backgroundLayer)
        addChild(gameLayer)
        addChild(uiLayer)
        
        // Setup physics
        physicsWorld.gravity = CGVector.zero
        physicsWorld.contactDelegate = self
    }
    
    private func loadAssets() {
        bulbOffTexture = SKTexture(imageNamed: "bulb_off")
        bulbOnTexture = SKTexture(imageNamed: "bulb_on")
        starTexture = SKTexture(imageNamed: "star")
        gremlinTexture = SKTexture(imageNamed: "gremlin")
    }
    
    // MARK: - Game Loop
    override func update(_ currentTime: TimeInterval) {
        if lastUpdateTime == 0 {
            lastUpdateTime = currentTime
        }
        
        let deltaTime = currentTime - lastUpdateTime
        lastUpdateTime = currentTime
        
        switch currentPhase {
        case .playing:
            updateGame(deltaTime: deltaTime)
        case .paused, .gameOver, .startScreen, .difficultySelection:
            break
        }
        
        screenShake.update()
    }
    
    private func updateGame(deltaTime: TimeInterval) {
        // Update game state
        gameState.update(deltaTime: deltaTime)
        
        // Update spawn timers
        updateSpawnTimers(deltaTime: deltaTime)
        
        // Update all game objects
        updateGameObjects()
        
        // Check for collisions
        checkCollisions()
        
        // Update UI
        updateUI()
        
        // Check for game over
        if gameState.isGameOver {
            currentPhase = .gameOver
            showGameOverScreen()
        }
    }
    
    private func updateSpawnTimers(deltaTime: TimeInterval) {
        // Spawn lightbulbs
        bulbSpawnTimer += deltaTime
        if bulbSpawnTimer >= bulbSpawnInterval {
            spawnLightbulb()
            bulbSpawnTimer = 0
        }
        
        // Spawn stars
        starSpawnTimer += deltaTime
        if starSpawnTimer >= currentStarInterval {
            spawnStar()
            starSpawnTimer = 0
        }
        
        // Spawn gremlins
        gremlinSpawnTimer += deltaTime
        if gremlinSpawnTimer >= gameState.gremlinSpawnInterval {
            spawnGremlin()
            gremlinSpawnTimer = 0
        }
        
        // Spawn powerups
        powerupSpawnTimer += deltaTime
        if powerupSpawnTimer >= powerupSpawnInterval {
            spawnPowerup()
            powerupSpawnTimer = 0
        }
    }
    
    private func updateGameObjects() {
        // Update lightbulbs
        for lightbulb in lightbulbs {
            lightbulb.update()
        }
        lightbulbs.removeAll { $0.parent == nil }
        
        // Update stars
        for star in stars {
            star.update()
        }
        stars.removeAll { $0.parent == nil }
        
        // Update gremlins
        for gremlin in gremlins {
            gremlin.update()
        }
        gremlins.removeAll { $0.parent == nil }
        
        // Update powerups
        for powerup in powerups {
            powerup.update()
        }
        powerups.removeAll { $0.parent == nil }
        
        // Update particles
        for particle in particles {
            particle.update()
        }
        particles.removeAll { $0.parent == nil }
        
        // Check for broken bulbs
        checkBrokenBulbs()
    }
    
    private func checkBrokenBulbs() {
        for lightbulb in lightbulbs {
            if lightbulb.position.y < -lightbulb.size.height {
                let brightnessLoss = lightbulb.onBreak()
                if brightnessLoss > 0 && !gameState.shieldActive {
                    gameState.brightness -= brightnessLoss
                    AudioManager.shared.playBulbBreak()
                }
                lightbulb.removeFromParent()
            }
        }
    }
    
    private func checkCollisions() {
        // Gremlins vs Lightbulbs
        for gremlin in gremlins {
            for lightbulb in lightbulbs {
                if gremlin.frame.intersects(lightbulb.frame) {
                    AudioManager.shared.playBulbBreak()
                    if lightbulb.isOn {
                        gameState.brightness -= GameConstants.bulbOnValue
                        gameState.litBulbCount = max(0, gameState.litBulbCount - 1)
                        gameState.updateOvercharge()
                    }
                    lightbulb.removeFromParent()
                    if let index = lightbulbs.firstIndex(of: lightbulb) {
                        lightbulbs.remove(at: index)
                    }
                }
            }
        }
        
        // Gremlins vs Stars
        for gremlin in gremlins {
            for star in stars {
                if gremlin.frame.intersects(star.frame) {
                    AudioManager.shared.playStarCollect()
                    star.removeFromParent()
                    if let index = stars.firstIndex(of: star) {
                        stars.remove(at: index)
                    }
                }
            }
        }
    }
    
    private func updateUI() {
        // Update star spawn rate based on overcharge
        updateStarMultiplier()

        // Apply screen shake
        let shakeOffset = screenShake.getOffset()
        gameLayer.position = shakeOffset

        // Update in-game UI
        if currentPhase == .playing {
            updateInGameUI()
        }
    }

    private func updateInGameUI() {
        // Clear existing UI
        uiLayer.removeAllChildren()

        // Create top bar background
        let topBar = SKShapeNode(rect: CGRect(x: 0, y: size.height - 50, width: size.width, height: 50))
        topBar.fillColor = UIColor(white: 0, alpha: 0.7)
        topBar.strokeColor = .clear
        uiLayer.addChild(topBar)

        // Score
        let scoreLabel = SKLabelNode(fontNamed: "Arial")
        scoreLabel.text = "Score: \(gameState.score)"
        scoreLabel.fontSize = 20
        scoreLabel.fontColor = GameConstants.white
        scoreLabel.position = CGPoint(x: 10, y: size.height - 35)
        scoreLabel.horizontalAlignmentMode = .left
        uiLayer.addChild(scoreLabel)

        // High Score
        let highScore = gameState.getHighScore(for: gameState.difficulty)
        let highScoreLabel = SKLabelNode(fontNamed: "Arial")
        highScoreLabel.text = "Best: \(highScore)"
        highScoreLabel.fontSize = 16
        highScoreLabel.fontColor = GameConstants.yellow
        highScoreLabel.position = CGPoint(x: 120, y: size.height - 35)
        highScoreLabel.horizontalAlignmentMode = .left
        uiLayer.addChild(highScoreLabel)

        // Difficulty
        let difficultyLabel = SKLabelNode(fontNamed: "Arial")
        difficultyLabel.text = gameState.difficulty.displayName
        difficultyLabel.fontSize = 16
        difficultyLabel.fontColor = GameConstants.cyan
        difficultyLabel.position = CGPoint(x: 200, y: size.height - 35)
        difficultyLabel.horizontalAlignmentMode = .left
        uiLayer.addChild(difficultyLabel)

        // Combo counter (only show if active)
        if gameState.comboCount > 0 {
            let comboLabel = SKLabelNode(fontNamed: "Arial")
            comboLabel.text = "Combo: \(gameState.comboCount)x"
            comboLabel.fontSize = 16
            comboLabel.fontColor = GameConstants.orange
            comboLabel.position = CGPoint(x: 280, y: size.height - 35)
            comboLabel.horizontalAlignmentMode = .left
            uiLayer.addChild(comboLabel)
        }

        // Overcharge indicator (only show if overcharged)
        if gameState.isOvercharged() {
            let multiplier = gameState.getOverchargeMultiplier()
            let overchargeLabel = SKLabelNode(fontNamed: "Arial")
            overchargeLabel.text = "⚡\(gameState.overchargeLevel) (\(String(format: "%.1f", multiplier))x)"
            overchargeLabel.fontSize = 16
            overchargeLabel.fontColor = GameConstants.yellow
            overchargeLabel.position = CGPoint(x: 360, y: size.height - 35)
            overchargeLabel.horizontalAlignmentMode = .left
            uiLayer.addChild(overchargeLabel)
        }

        // Brightness bar
        let barWidth: CGFloat = 150
        let barHeight: CGFloat = 16
        let barX = size.width - barWidth - 80
        let barY = size.height - 35

        // Background
        let barBackground = SKShapeNode(rect: CGRect(x: barX, y: barY - barHeight/2, width: barWidth, height: barHeight))
        barBackground.fillColor = UIColor(white: 0.3, alpha: 1.0)
        barBackground.strokeColor = GameConstants.white
        barBackground.lineWidth = 2
        uiLayer.addChild(barBackground)

        // Fill
        let fillWidth = (CGFloat(gameState.brightness) / 100.0) * barWidth
        let barFill = SKShapeNode(rect: CGRect(x: barX, y: barY - barHeight/2, width: fillWidth, height: barHeight))

        if gameState.brightness > 60 {
            barFill.fillColor = GameConstants.green
        } else if gameState.brightness > 30 {
            barFill.fillColor = GameConstants.yellow
        } else {
            barFill.fillColor = GameConstants.red
        }
        barFill.strokeColor = .clear
        uiLayer.addChild(barFill)

        // Brightness percentage
        let brightnessLabel = SKLabelNode(fontNamed: "Arial")
        brightnessLabel.text = "\(Int(gameState.brightness))%"
        brightnessLabel.fontSize = 16
        brightnessLabel.fontColor = GameConstants.white
        brightnessLabel.position = CGPoint(x: barX + barWidth + 10, y: size.height - 35)
        brightnessLabel.horizontalAlignmentMode = .left
        uiLayer.addChild(brightnessLabel)

        // Shield indicator
        if gameState.shieldActive {
            let shieldLabel = SKLabelNode(fontNamed: "Arial")
            shieldLabel.text = "🛡 Shield"
            shieldLabel.fontSize = 16
            shieldLabel.fontColor = GameConstants.blue
            shieldLabel.position = CGPoint(x: size.width - 10, y: size.height - 60)
            shieldLabel.horizontalAlignmentMode = .right
            uiLayer.addChild(shieldLabel)
        }
    }
    
    private func updateStarMultiplier() {
        let newInterval: TimeInterval
        if gameState.overchargeLevel >= 4 {
            newInterval = baseStarInterval * 0.3
        } else if gameState.overchargeLevel >= 2 {
            newInterval = baseStarInterval * 0.5
        } else if gameState.overchargeLevel >= 1 {
            newInterval = baseStarInterval * 0.7
        } else {
            newInterval = baseStarInterval
        }
        
        if abs(newInterval - currentStarInterval) > 0.2 {
            currentStarInterval = newInterval
        }
    }

    // MARK: - Spawning Methods
    private func spawnLightbulb() {
        let lightbulb = Lightbulb(offTexture: bulbOffTexture, onTexture: bulbOnTexture)
        lightbulb.gameScene = self
        gameLayer.addChild(lightbulb)
        lightbulbs.append(lightbulb)
    }

    private func spawnStar() {
        // Determine if star should be unstable
        let isUnstable = gameState.isOvercharged() && Double.random(in: 0...1) < 0.7

        let star = Star(texture: starTexture, isUnstable: isUnstable)
        star.gameScene = self
        gameLayer.addChild(star)
        stars.append(star)

        // Spawn additional stars based on overcharge level
        let extraStars = gameState.getExtraStarCount()
        for _ in 0..<extraStars {
            let unstableChance = gameState.overchargeLevel >= 3 ? 0.8 : 0.6
            let extraUnstable = gameState.isOvercharged() && Double.random(in: 0...1) < unstableChance
            let extraStar = Star(texture: starTexture, isUnstable: extraUnstable)
            extraStar.gameScene = self
            gameLayer.addChild(extraStar)
            stars.append(extraStar)
        }
    }

    private func spawnGremlin() {
        if gremlins.count < gameState.maxGremlins {
            let gremlin = Gremlin(texture: gremlinTexture)
            gremlin.gameScene = self
            gameLayer.addChild(gremlin)
            gremlins.append(gremlin)
        }
    }

    private func spawnPowerup() {
        let powerType = PowerUp.PowerType.allCases.randomElement() ?? .freeze
        let powerup = PowerUp(powerType: powerType)
        powerup.gameScene = self
        gameLayer.addChild(powerup)
        powerups.append(powerup)
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        switch currentPhase {
        case .startScreen:
            showDifficultySelection()
        case .difficultySelection:
            handleDifficultySelection(at: location)
        case .playing:
            handleGameTouch(at: location)
        case .paused:
            resumeGame()
        case .gameOver:
            showStartScreen()
        }
    }

    // Handle multiple touches for better responsiveness
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        // Handle any touch-up events if needed
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        // Handle cancelled touches
    }

    // Add gesture recognizer support for pause gesture (long press)
    private func setupGestureRecognizers() {
        guard let view = view else { return }

        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPressGesture.minimumPressDuration = 1.0
        view.addGestureRecognizer(longPressGesture)
    }

    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began && currentPhase == .playing {
            pauseGame()
        }
    }

    private func handleGameTouch(at location: CGPoint) {
        let touchedNodes = nodes(at: location)

        // Check for power-up touches first
        for node in touchedNodes {
            if let powerup = node as? PowerUp {
                activatePowerup(powerup)
                return
            }
        }

        // Check for gremlin touches
        for node in touchedNodes {
            if let gremlin = node as? Gremlin {
                AudioManager.shared.playGremlinHit()
                screenShake.addShake(intensity: 3, duration: 10)
                gremlin.removeFromParent()
                if let index = gremlins.firstIndex(of: gremlin) {
                    gremlins.remove(at: index)
                }
                gameState.addCombo()
                return
            }
        }

        // Check for lightbulb touches
        for node in touchedNodes {
            if let lightbulb = node as? Lightbulb {
                if let glowPosition = lightbulb.turnOn() {
                    AudioManager.shared.playBulbClick()
                    gameState.brightness += GameConstants.bulbOnValue
                    let glow = Glow(center: glowPosition)
                    gameLayer.addChild(glow)
                    gameState.addCombo()
                }
                return
            }
        }

        // Check for star touches
        for node in touchedNodes {
            if let star = node as? Star, star.visible {
                gameState.addScore(basePoints: 1)
                AudioManager.shared.playStarCollect()

                // Create particle explosion
                let particleCount = star.isUnstable ? 30 : 20
                for _ in 0..<particleCount {
                    let particle = Particle(position: star.position)
                    gameLayer.addChild(particle)
                    particles.append(particle)
                }

                star.removeFromParent()
                if let index = stars.firstIndex(of: star) {
                    stars.remove(at: index)
                }
                gameState.addCombo()
                return
            }
        }
    }

    // MARK: - Game Object Callbacks
    func bulbTurnedOn(_ lightbulb: Lightbulb) {
        gameState.litBulbCount += 1
        gameState.updateOvercharge()
    }

    func bulbBroken(_ lightbulb: Lightbulb) {
        gameState.litBulbCount = max(0, gameState.litBulbCount - 1)
        gameState.updateOvercharge()
    }

    // MARK: - Power-up System
    private func activatePowerup(_ powerup: PowerUp) {
        switch powerup.powerType {
        case .freeze:
            // Freeze all gremlins for 3 seconds
            for gremlin in gremlins {
                gremlin.freeze(duration: 180) // 3 seconds at 60 FPS
            }
            let glow = Glow(center: CGPoint(x: size.width/2, y: size.height/2), color: GameConstants.cyan)
            gameLayer.addChild(glow)

        case .shield:
            // Activate shield for 5 seconds
            gameState.activateShield(duration: 5.0)
            let glow = Glow(center: CGPoint(x: size.width/2, y: size.height/2), color: GameConstants.blue)
            gameLayer.addChild(glow)

        case .boost:
            // Instant brightness boost
            gameState.brightness = min(100, gameState.brightness + 50)
            let glow = Glow(center: CGPoint(x: size.width/2, y: size.height/2), color: GameConstants.yellow)
            gameLayer.addChild(glow)

        case .multi:
            // Turn on all lightbulbs currently on screen
            for lightbulb in lightbulbs {
                if !lightbulb.isOn {
                    if let glowPosition = lightbulb.turnOn() {
                        gameState.brightness += GameConstants.bulbOnValue
                        let glow = Glow(center: glowPosition, color: GameConstants.green)
                        gameLayer.addChild(glow)
                    }
                }
            }
        }

        powerup.removeFromParent()
        if let index = powerups.firstIndex(of: powerup) {
            powerups.remove(at: index)
        }
    }

    // MARK: - Game Control
    private func pauseGame() {
        currentPhase = .paused
        gameState.pauseGame()
        showPauseOverlay()
    }

    private func resumeGame() {
        currentPhase = .playing
        gameState.resumeGame()
        hidePauseOverlay()
    }

    private func startGame(difficulty: Difficulty) {
        currentPhase = .playing
        gameState.startGame(difficulty: difficulty)

        // Clear existing game objects
        clearGameObjects()

        // Spawn initial gremlins
        for _ in 0..<gameState.difficultySettings.initialGremlins {
            spawnGremlin()
        }

        // Reset timers
        bulbSpawnTimer = 0
        starSpawnTimer = 0
        gremlinSpawnTimer = 0
        powerupSpawnTimer = 0
    }

    private func clearGameObjects() {
        for lightbulb in lightbulbs {
            lightbulb.removeFromParent()
        }
        lightbulbs.removeAll()

        for star in stars {
            star.removeFromParent()
        }
        stars.removeAll()

        for gremlin in gremlins {
            gremlin.removeFromParent()
        }
        gremlins.removeAll()

        for powerup in powerups {
            powerup.removeFromParent()
        }
        powerups.removeAll()

        for particle in particles {
            particle.removeFromParent()
        }
        particles.removeAll()
    }

    // MARK: - UI Methods
    private func showStartScreen() {
        currentPhase = .startScreen
        clearUI()

        let titleLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
        titleLabel.text = "Turn Me On"
        titleLabel.fontSize = 64
        titleLabel.fontColor = GameConstants.yellow
        titleLabel.position = CGPoint(x: size.width/2, y: size.height * 0.75)
        uiLayer.addChild(titleLabel)

        let instructionLabel1 = SKLabelNode(fontNamed: "Arial")
        instructionLabel1.text = "Click bulbs to keep the lights on."
        instructionLabel1.fontSize = 28
        instructionLabel1.fontColor = GameConstants.white
        instructionLabel1.position = CGPoint(x: size.width/2, y: size.height/2 - 50)
        uiLayer.addChild(instructionLabel1)

        let instructionLabel2 = SKLabelNode(fontNamed: "Arial")
        instructionLabel2.text = "Click blue stars for points."
        instructionLabel2.fontSize = 28
        instructionLabel2.fontColor = GameConstants.white
        instructionLabel2.position = CGPoint(x: size.width/2, y: size.height/2)
        uiLayer.addChild(instructionLabel2)

        let instructionLabel3 = SKLabelNode(fontNamed: "Arial")
        instructionLabel3.text = "AVOID the purple gremlins!"
        instructionLabel3.fontSize = 28
        instructionLabel3.fontColor = GameConstants.purple
        instructionLabel3.position = CGPoint(x: size.width/2, y: size.height/2 + 50)
        uiLayer.addChild(instructionLabel3)

        let continueLabel = SKLabelNode(fontNamed: "Arial")
        continueLabel.text = "Tap to continue"
        continueLabel.fontSize = 32
        continueLabel.fontColor = GameConstants.green
        continueLabel.position = CGPoint(x: size.width/2, y: size.height * 0.25)
        uiLayer.addChild(continueLabel)
    }

    private func showDifficultySelection() {
        currentPhase = .difficultySelection
        clearUI()

        let titleLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
        titleLabel.text = "Choose Your Difficulty"
        titleLabel.fontSize = 48
        titleLabel.fontColor = GameConstants.yellow
        titleLabel.position = CGPoint(x: size.width/2, y: size.height * 0.8)
        uiLayer.addChild(titleLabel)

        // Create difficulty buttons
        createDifficultyButton(difficulty: .easy, position: CGPoint(x: size.width/2, y: size.height * 0.6), color: GameConstants.green)
        createDifficultyButton(difficulty: .okay, position: CGPoint(x: size.width/2, y: size.height * 0.45), color: GameConstants.blue)
        createDifficultyButton(difficulty: .madMan, position: CGPoint(x: size.width/2, y: size.height * 0.3), color: GameConstants.red)

        let instructionLabel = SKLabelNode(fontNamed: "Arial")
        instructionLabel.text = "Tap a difficulty to start"
        instructionLabel.fontSize = 24
        instructionLabel.fontColor = GameConstants.white
        instructionLabel.position = CGPoint(x: size.width/2, y: size.height * 0.15)
        uiLayer.addChild(instructionLabel)
    }

    private func createDifficultyButton(difficulty: Difficulty, position: CGPoint, color: UIColor) {
        let buttonBackground = SKShapeNode(rectOf: CGSize(width: 300, height: 80), cornerRadius: 10)
        buttonBackground.fillColor = UIColor(white: 0.2, alpha: 0.8)
        buttonBackground.strokeColor = color
        buttonBackground.lineWidth = 2
        buttonBackground.position = position
        buttonBackground.name = "difficulty_\(difficulty.rawValue)"

        let titleLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
        titleLabel.text = difficulty.displayName
        titleLabel.fontSize = 36
        titleLabel.fontColor = color
        titleLabel.position = CGPoint(x: 0, y: 10)
        titleLabel.verticalAlignmentMode = .center
        buttonBackground.addChild(titleLabel)

        let descLabel = SKLabelNode(fontNamed: "Arial")
        descLabel.text = difficulty.description
        descLabel.fontSize = 18
        descLabel.fontColor = GameConstants.white
        descLabel.position = CGPoint(x: 0, y: -15)
        descLabel.verticalAlignmentMode = .center
        buttonBackground.addChild(descLabel)

        let highScore = gameState.getHighScore(for: difficulty)
        let scoreLabel = SKLabelNode(fontNamed: "Arial")
        scoreLabel.text = "Best: \(highScore)"
        scoreLabel.fontSize = 16
        scoreLabel.fontColor = GameConstants.yellow
        scoreLabel.position = CGPoint(x: 0, y: -35)
        scoreLabel.verticalAlignmentMode = .center
        buttonBackground.addChild(scoreLabel)

        uiLayer.addChild(buttonBackground)
    }

    private func handleDifficultySelection(at location: CGPoint) {
        let touchedNodes = nodes(at: location)

        for node in touchedNodes {
            if let nodeName = node.name, nodeName.hasPrefix("difficulty_") {
                let difficultyString = String(nodeName.dropFirst("difficulty_".count))
                if let difficulty = Difficulty(rawValue: difficultyString) {
                    startGame(difficulty: difficulty)
                    return
                }
            }
        }
    }

    private func showGameOverScreen() {
        clearUI()

        let gameOverLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
        gameOverLabel.text = "GAME OVER"
        gameOverLabel.fontSize = 64
        gameOverLabel.fontColor = GameConstants.red
        gameOverLabel.position = CGPoint(x: size.width/2, y: size.height * 0.7)
        uiLayer.addChild(gameOverLabel)

        let scoreLabel = SKLabelNode(fontNamed: "Arial")
        scoreLabel.text = "Final Score: \(gameState.score)"
        scoreLabel.fontSize = 36
        scoreLabel.fontColor = GameConstants.white
        scoreLabel.position = CGPoint(x: size.width/2, y: size.height * 0.55)
        uiLayer.addChild(scoreLabel)

        let timeLabel = SKLabelNode(fontNamed: "Arial")
        timeLabel.text = "Survival Time: \(Int(gameState.gameTime))s"
        timeLabel.fontSize = 28
        timeLabel.fontColor = GameConstants.white
        timeLabel.position = CGPoint(x: size.width/2, y: size.height * 0.48)
        uiLayer.addChild(timeLabel)

        if gameState.isNewHighScore() {
            let newHighScoreLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
            newHighScoreLabel.text = "NEW HIGH SCORE!"
            newHighScoreLabel.fontSize = 32
            newHighScoreLabel.fontColor = GameConstants.yellow
            newHighScoreLabel.position = CGPoint(x: size.width/2, y: size.height * 0.4)
            uiLayer.addChild(newHighScoreLabel)
        } else {
            let highScoreLabel = SKLabelNode(fontNamed: "Arial")
            highScoreLabel.text = "High Score: \(gameState.getHighScore(for: gameState.difficulty))"
            highScoreLabel.fontSize = 24
            highScoreLabel.fontColor = GameConstants.yellow
            highScoreLabel.position = CGPoint(x: size.width/2, y: size.height * 0.4)
            uiLayer.addChild(highScoreLabel)
        }

        let restartLabel = SKLabelNode(fontNamed: "Arial")
        restartLabel.text = "Tap to Restart"
        restartLabel.fontSize = 30
        restartLabel.fontColor = GameConstants.green
        restartLabel.position = CGPoint(x: size.width/2, y: size.height * 0.25)
        uiLayer.addChild(restartLabel)
    }

    private func showPauseOverlay() {
        let overlay = SKShapeNode(rect: CGRect(x: 0, y: 50, width: size.width, height: size.height - 50))
        overlay.fillColor = UIColor(white: 0, alpha: 0.5)
        overlay.strokeColor = .clear
        overlay.name = "pauseOverlay"
        uiLayer.addChild(overlay)

        let pauseLabel = SKLabelNode(fontNamed: "Arial-BoldMT")
        pauseLabel.text = "PAUSED"
        pauseLabel.fontSize = 64
        pauseLabel.fontColor = GameConstants.white
        pauseLabel.position = CGPoint(x: size.width/2, y: size.height/2 + 30)
        uiLayer.addChild(pauseLabel)

        let resumeLabel = SKLabelNode(fontNamed: "Arial")
        resumeLabel.text = "Tap to Resume"
        resumeLabel.fontSize = 32
        resumeLabel.fontColor = GameConstants.white
        resumeLabel.position = CGPoint(x: size.width/2, y: size.height/2 - 40)
        uiLayer.addChild(resumeLabel)
    }

    private func hidePauseOverlay() {
        uiLayer.childNode(withName: "pauseOverlay")?.removeFromParent()
        uiLayer.children.filter { $0 is SKLabelNode }.forEach { $0.removeFromParent() }
    }

    private func clearUI() {
        uiLayer.removeAllChildren()
    }
}

// MARK: - Physics Contact Delegate
extension GameScene: SKPhysicsContactDelegate {
    func didBegin(_ contact: SKPhysicsContact) {
        // Handle physics-based collisions if needed
        // Currently using frame-based collision detection
    }
}
