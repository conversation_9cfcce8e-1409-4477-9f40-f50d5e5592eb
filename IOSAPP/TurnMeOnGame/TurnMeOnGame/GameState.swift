import Foundation

// MARK: - Difficulty Settings
enum Difficulty: String, CaseIterable {
    case easy, okay, madMan = "mad_man"
    
    var displayName: String {
        switch self {
        case .easy: return "Easy"
        case .okay: return "Okay"
        case .madMan: return "Mad Man"
        }
    }
    
    var description: String {
        switch self {
        case .easy: return "Fewer gremlins, slower pace"
        case .okay: return "Balanced challenge"
        case .madMan: return "INSANE gremlin chaos!"
        }
    }
}

struct DifficultySettings {
    let initialGremlins: Int
    let maxGremlins: Int
    let spawnInterval: TimeInterval
    let minSpawnInterval: TimeInterval
    let difficultyIncreaseTime: TimeInterval
    let brightnessDecay: Float
    
    static func settings(for difficulty: Difficulty) -> DifficultySettings {
        switch difficulty {
        case .easy:
            return DifficultySettings(
                initialGremlins: 1,
                maxGremlins: 4,
                spawnInterval: 6.0,
                minSpawnInterval: 3.0,
                difficultyIncreaseTime: 15.0,
                brightnessDecay: 0.10
            )
        case .okay:
            return DifficultySettings(
                initialGremlins: 1,
                maxGremlins: 6,
                spawnInterval: 4.0,
                minSpawnInterval: 2.0,
                difficultyIncreaseTime: 10.0,
                brightnessDecay: 0.15
            )
        case .madMan:
            return DifficultySettings(
                initialGremlins: 2,
                maxGremlins: 12,
                spawnInterval: 2.0,
                minSpawnInterval: 0.8,
                difficultyIncreaseTime: 5.0,
                brightnessDecay: 0.20
            )
        }
    }
}

// MARK: - Game State Manager
class GameState: ObservableObject {
    // Game state variables
    @Published var score: Int = 0
    @Published var brightness: Float = 100.0
    @Published var gameTime: TimeInterval = 0
    @Published var isGameOver: Bool = false
    @Published var isPaused: Bool = false
    
    // Difficulty and progression
    var difficulty: Difficulty = .okay
    var difficultySettings: DifficultySettings = DifficultySettings.settings(for: .okay)
    var maxGremlins: Int = 1
    var gremlinSpawnInterval: TimeInterval = 4.0
    
    // Combo system
    @Published var comboCount: Int = 0
    private var comboTimer: TimeInterval = 0
    private let comboTimeLimit: TimeInterval = 2.0
    
    // Overcharge system
    @Published var overchargeLevel: Int = 0
    @Published var litBulbCount: Int = 0
    
    // Power-up effects
    @Published var shieldActive: Bool = false
    private var shieldTimer: TimeInterval = 0
    
    // High scores
    private var highScores: [String: Int] = [:]
    
    init() {
        loadHighScores()
    }
    
    // MARK: - Game Control
    func startGame(difficulty: Difficulty) {
        self.difficulty = difficulty
        self.difficultySettings = DifficultySettings.settings(for: difficulty)
        
        // Reset game state
        score = 0
        brightness = 100.0
        gameTime = 0
        isGameOver = false
        isPaused = false
        comboCount = 0
        comboTimer = 0
        overchargeLevel = 0
        litBulbCount = 0
        shieldActive = false
        shieldTimer = 0
        
        // Apply difficulty settings
        maxGremlins = difficultySettings.initialGremlins
        gremlinSpawnInterval = difficultySettings.spawnInterval
    }
    
    func pauseGame() {
        isPaused = true
    }
    
    func resumeGame() {
        isPaused = false
    }
    
    func endGame() {
        isGameOver = true
        saveHighScore()
    }
    
    // MARK: - Game Updates
    func update(deltaTime: TimeInterval) {
        guard !isPaused && !isGameOver else { return }
        
        gameTime += deltaTime
        
        // Update combo timer
        updateCombo(deltaTime: deltaTime)
        
        // Update power-up timers
        updatePowerUpTimers(deltaTime: deltaTime)
        
        // Update difficulty
        updateDifficulty()
        
        // Brightness decay (unless shield is active)
        if !shieldActive {
            brightness -= difficultySettings.brightnessDecay * Float(deltaTime) * 60
            brightness = max(0, min(100, brightness))
        }
        
        // Check for game over
        if brightness <= 0 {
            endGame()
        }
    }
    
    // MARK: - Combo System
    func addCombo() {
        comboCount += 1
        comboTimer = comboTimeLimit
    }
    
    private func updateCombo(deltaTime: TimeInterval) {
        if comboTimer > 0 {
            comboTimer -= deltaTime
        } else {
            comboCount = 0
        }
    }
    
    // MARK: - Overcharge System
    func updateOvercharge() {
        overchargeLevel = max(0, litBulbCount - 2)
    }
    
    func getOverchargeMultiplier() -> Float {
        if overchargeLevel >= 5 {
            return 3.0
        } else if overchargeLevel >= 3 {
            return 2.5
        } else if overchargeLevel >= 2 {
            return 2.0
        } else if overchargeLevel >= 1 {
            return 1.5
        } else {
            return 1.0
        }
    }
    
    func getExtraStarCount() -> Int {
        if overchargeLevel >= 4 {
            return 3
        } else if overchargeLevel >= 2 {
            return 2
        } else if overchargeLevel >= 1 {
            return 1
        } else {
            return 0
        }
    }
    
    func isOvercharged() -> Bool {
        return overchargeLevel > 0
    }
    
    // MARK: - Power-ups
    func activateShield(duration: TimeInterval = 5.0) {
        shieldActive = true
        shieldTimer = duration
    }
    
    private func updatePowerUpTimers(deltaTime: TimeInterval) {
        if shieldActive {
            shieldTimer -= deltaTime
            if shieldTimer <= 0 {
                shieldActive = false
            }
        }
    }
    
    // MARK: - Difficulty Progression
    private func updateDifficulty() {
        let difficultyLevel = Int(gameTime / difficultySettings.difficultyIncreaseTime)
        
        // Increase max gremlins
        let newMaxGremlins = min(
            difficultySettings.initialGremlins + difficultyLevel,
            difficultySettings.maxGremlins
        )
        maxGremlins = newMaxGremlins
        
        // Decrease spawn interval (faster spawning)
        let intervalDecrease = TimeInterval(difficultyLevel) * (difficultySettings.spawnInterval / 10)
        gremlinSpawnInterval = max(
            difficultySettings.spawnInterval - intervalDecrease,
            difficultySettings.minSpawnInterval
        )
    }
    
    // MARK: - Scoring
    func addScore(basePoints: Int) {
        let multiplier = getOverchargeMultiplier()
        let comboBonus = comboCount / 3
        let totalPoints = Int(Float(basePoints + comboBonus) * multiplier)
        score += totalPoints
    }
    
    // MARK: - High Scores
    private func loadHighScores() {
        if let data = UserDefaults.standard.data(forKey: "HighScores"),
           let scores = try? JSONDecoder().decode([String: Int].self, from: data) {
            highScores = scores
        } else {
            highScores = ["easy": 0, "okay": 0, "mad_man": 0]
        }
    }
    
    private func saveHighScore() {
        let key = difficulty.rawValue
        if score > (highScores[key] ?? 0) {
            highScores[key] = score
            
            if let data = try? JSONEncoder().encode(highScores) {
                UserDefaults.standard.set(data, forKey: "HighScores")
            }
        }
    }
    
    func getHighScore(for difficulty: Difficulty) -> Int {
        return highScores[difficulty.rawValue] ?? 0
    }
    
    func isNewHighScore() -> Bool {
        return score > getHighScore(for: difficulty) && score > 0
    }
}
