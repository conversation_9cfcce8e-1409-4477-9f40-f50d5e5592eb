<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.SpriteKit.Scene" version="3.0" toolsVersion="22155" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22131"/>
    </dependencies>
    <scenes>
        <scene sceneID="tne-QT-ifu">
            <objects>
                <scene class="GameScene" id="hHg-GG-vHW" customClass="GameScene" customModule="TurnMeOnGame">
                    <size key="contentSize" width="393" height="852"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </scene>
            </objects>
            <point key="canvasLocation" x="130" y="133"/>
        </scene>
    </scenes>
</document>
