import UIKit
import SpriteKit
import GameplayKit

class GameViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        if let view = self.view as! SKView? {
            // Create the game scene with proper size
            let scene = GameScene(size: view.bounds.size)
            scene.scaleMode = .aspectFill

            // Present the scene
            view.presentScene(scene)

            view.ignoresSiblingOrder = true
            view.showsFPS = false
            view.showsNodeCount = false
            view.isMultipleTouchEnabled = false // Single touch for this game
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // Hide navigation bar if present
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // Ensure the scene size matches the view
        if let skView = view as? SKView,
           let scene = skView.scene {
            scene.size = skView.bounds.size
        }
    }

    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if UIDevice.current.userInterfaceIdiom == .phone {
            return .portrait
        } else {
            return [.portrait, .landscapeLeft, .landscapeRight]
        }
    }

    override var shouldAutorotate: Bool {
        return UIDevice.current.userInterfaceIdiom == .pad
    }

    override var prefersStatusBarHidden: Bool {
        return true
    }
}
