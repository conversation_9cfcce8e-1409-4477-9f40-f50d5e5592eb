import SpriteKit
import Foundation

// MARK: - Game Constants
struct GameConstants {
    static let screenWidth: CGFloat = 480
    static let screenHeight: CGFloat = 800
    static let brightnessDecay: Float = 0.15
    static let bulbOnValue: Float = 25
    
    // Colors
    static let white = UIColor.white
    static let black = UIColor.black
    static let red = UIColor.red
    static let green = UIColor.green
    static let blue = UIColor.blue
    static let yellow = UIColor.yellow
    static let purple = UIColor.purple
    static let orange = UIColor.orange
    static let cyan = UIColor.cyan
    static let pink = UIColor.systemPink
}

// MARK: - Glow Effect
class Glow: SKNode {
    private var circle: SKShapeNode
    private var radius: CGFloat = 10
    private let maxRadius: CGFloat = 50
    private let speed: CGFloat = 4
    
    init(center: CGPoint, color: UIColor = GameConstants.yellow) {
        circle = SKShapeNode(circleOfRadius: 10)
        super.init()
        
        position = center
        circle.fillColor = color
        circle.strokeColor = .clear
        circle.alpha = 1.0
        addChild(circle)
        
        startAnimation()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func startAnimation() {
        let expandAction = SKAction.scale(to: maxRadius / radius, duration: 1.0)
        let fadeAction = SKAction.fadeOut(withDuration: 1.0)
        let groupAction = SKAction.group([expandAction, fadeAction])
        let removeAction = SKAction.removeFromParent()
        let sequence = SKAction.sequence([groupAction, removeAction])
        
        run(sequence)
    }
}

// MARK: - Screen Shake
class ScreenShake {
    private var shakeIntensity: CGFloat = 0
    private var shakeDuration: Int = 0
    
    func addShake(intensity: CGFloat, duration: Int) {
        shakeIntensity = max(shakeIntensity, intensity)
        shakeDuration = max(shakeDuration, duration)
    }
    
    func update() {
        if shakeDuration > 0 {
            shakeDuration -= 1
            if shakeDuration <= 0 {
                shakeIntensity = 0
            }
        }
    }
    
    func getOffset() -> CGPoint {
        if shakeIntensity > 0 {
            let x = CGFloat.random(in: -shakeIntensity...shakeIntensity)
            let y = CGFloat.random(in: -shakeIntensity...shakeIntensity)
            return CGPoint(x: x, y: y)
        }
        return CGPoint.zero
    }
}

// MARK: - Falling Object Base Class
class FallingObject: SKSpriteNode {
    var speed: CGFloat = 0
    
    override init(texture: SKTexture?, color: UIColor, size: CGSize) {
        super.init(texture: texture, color: color, size: size)
        speed = CGFloat.random(in: 2...6)
        setupPhysics()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupPhysics() {
        physicsBody = SKPhysicsBody(rectangleOf: size)
        physicsBody?.isDynamic = true
        physicsBody?.affectedByGravity = false
        physicsBody?.categoryBitMask = 1
        physicsBody?.contactTestBitMask = 0
        physicsBody?.collisionBitMask = 0
    }
    
    func update() {
        position.y -= speed
        
        // Remove if off screen
        if position.y < -size.height {
            removeFromParent()
        }
    }
}

// MARK: - Lightbulb
class Lightbulb: FallingObject {
    private let offTexture: SKTexture
    private let onTexture: SKTexture
    private(set) var isOn: Bool = false
    weak var gameScene: GameScene?
    
    init(offTexture: SKTexture, onTexture: SKTexture) {
        self.offTexture = offTexture
        self.onTexture = onTexture
        
        super.init(texture: offTexture, color: .clear, size: CGSize(width: 40, height: 60))
        
        // Random starting position
        let randomX = CGFloat.random(in: size.width/2...(GameConstants.screenWidth - size.width/2))
        position = CGPoint(x: randomX, y: GameConstants.screenHeight + size.height)
        
        name = "lightbulb"
        isUserInteractionEnabled = true
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func turnOn() -> CGPoint? {
        if !isOn {
            isOn = true
            texture = onTexture
            gameScene?.bulbTurnedOn(self)
            return position
        }
        return nil
    }
    
    func onBreak() -> Float {
        var brightnessLoss: Float = 0
        if isOn {
            brightnessLoss = GameConstants.bulbOnValue
            gameScene?.bulbBroken(self)
        }
        return brightnessLoss
    }
}

// MARK: - Particle
class Particle: SKSpriteNode {
    private var velocity: CGVector
    private var lifespan: Int = 40
    private let gravity: CGFloat = 0.3
    
    init(position: CGPoint) {
        let size = CGSize(width: CGFloat.random(in: 4...8), height: CGFloat.random(in: 4...8))
        let colors = [GameConstants.blue, GameConstants.white, UIColor(red: 0.4, green: 0.4, blue: 1.0, alpha: 1.0)]
        let color = colors.randomElement() ?? GameConstants.blue
        
        velocity = CGVector(
            dx: CGFloat.random(in: -5...5),
            dy: CGFloat.random(in: -7...(-1))
        )
        
        super.init(texture: nil, color: color, size: size)
        self.position = position
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func update() {
        lifespan -= 1
        if lifespan <= 0 {
            removeFromParent()
            return
        }
        
        velocity.dy += gravity
        position.x += velocity.dx
        position.y += velocity.dy
    }
}

// MARK: - Star
class Star: FallingObject {
    private let originalTexture: SKTexture
    private(set) var isUnstable: Bool = false
    private var stabilityTimer: Int = 300
    private var flickerTimer: Int = 0
    private(set) var visible: Bool = true
    weak var gameScene: GameScene?
    
    init(texture: SKTexture, isUnstable: Bool = false) {
        self.originalTexture = texture
        self.isUnstable = isUnstable
        
        super.init(texture: texture, color: .clear, size: CGSize(width: 35, height: 35))
        
        // Random starting position
        let randomX = CGFloat.random(in: size.width/2...(GameConstants.screenWidth - size.width/2))
        position = CGPoint(x: randomX, y: GameConstants.screenHeight + size.height)
        speed = CGFloat.random(in: 1...4)
        
        name = "star"
        isUserInteractionEnabled = true
        
        if isUnstable {
            createUnstableAppearance()
        }
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func createUnstableAppearance() {
        // Tint unstable stars slightly red
        color = UIColor(red: 1.0, green: 0.4, blue: 0.4, alpha: 0.3)
        colorBlendFactor = 0.3
    }
    
    override func update() {
        if isUnstable {
            stabilityTimer -= 1
            
            // Flicker effect as star becomes more unstable
            if stabilityTimer < 120 { // Last 2 seconds
                flickerTimer += 1
                if flickerTimer % 10 == 0 {
                    visible = !visible
                    alpha = visible ? 1.0 : 0.0
                }
            }
            
            // Star disappears when timer runs out
            if stabilityTimer <= 0 {
                removeFromParent()
                return
            }
        }
        
        if visible {
            super.update()
        } else {
            position.y -= speed
            if position.y < -size.height {
                removeFromParent()
            }
        }
    }
}

// MARK: - Gremlin
class Gremlin: SKSpriteNode {
    private var speedX: CGFloat
    private var speedY: CGFloat
    private var frozen: Bool = false
    private var frozenTimer: Int = 0
    weak var gameScene: GameScene?

    init(texture: SKTexture) {
        speedX = [-4, -3, -2, 2, 3, 4].randomElement() ?? 2
        speedY = [-4, -3, -2, 2, 3, 4].randomElement() ?? 2

        super.init(texture: texture, color: .clear, size: CGSize(width: 50, height: 50))

        // Random starting position on screen
        position = CGPoint(
            x: CGFloat.random(in: size.width/2...(GameConstants.screenWidth - size.width/2)),
            y: CGFloat.random(in: 50...(GameConstants.screenHeight - 100))
        )

        name = "gremlin"
        isUserInteractionEnabled = true
        setupPhysics()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupPhysics() {
        physicsBody = SKPhysicsBody(rectangleOf: size)
        physicsBody?.isDynamic = true
        physicsBody?.affectedByGravity = false
        physicsBody?.categoryBitMask = 2
        physicsBody?.contactTestBitMask = 1 | 4 // Contact with lightbulbs and stars
        physicsBody?.collisionBitMask = 0
    }

    func update() {
        // Handle freeze effect
        if frozen {
            frozenTimer -= 1
            if frozenTimer <= 0 {
                frozen = false
            }
            return
        }

        // Move the gremlin
        position.x += speedX
        position.y += speedY

        // Bounce off screen edges
        if position.x <= size.width/2 {
            position.x = size.width/2
            speedX *= -1
        }
        if position.x >= GameConstants.screenWidth - size.width/2 {
            position.x = GameConstants.screenWidth - size.width/2
            speedX *= -1
        }

        if position.y <= size.height/2 {
            position.y = size.height/2
            speedY *= -1
        }
        if position.y >= GameConstants.screenHeight - size.height/2 {
            position.y = GameConstants.screenHeight - size.height/2
            speedY *= -1
        }
    }

    func freeze(duration: Int) {
        frozen = true
        frozenTimer = duration
    }
}

// MARK: - PowerUp
class PowerUp: FallingObject {
    enum PowerType: String, CaseIterable {
        case freeze, shield, boost, multi
    }

    let powerType: PowerType
    weak var gameScene: GameScene?

    init(powerType: PowerType) {
        self.powerType = powerType

        super.init(texture: nil, color: .clear, size: CGSize(width: 30, height: 30))

        // Random starting position
        let randomX = CGFloat.random(in: size.width/2...(GameConstants.screenWidth - size.width/2))
        position = CGPoint(x: randomX, y: GameConstants.screenHeight + size.height)
        speed = CGFloat.random(in: 1...3)

        name = "powerup"
        isUserInteractionEnabled = true

        drawPowerUp()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func drawPowerUp() {
        let circle = SKShapeNode(circleOfRadius: 12)
        let innerCircle = SKShapeNode(circleOfRadius: 8)

        switch powerType {
        case .freeze:
            circle.fillColor = GameConstants.cyan
            innerCircle.fillColor = GameConstants.white
        case .shield:
            circle.fillColor = GameConstants.blue
            innerCircle.fillColor = GameConstants.white
        case .boost:
            circle.fillColor = GameConstants.yellow
            innerCircle.fillColor = GameConstants.white
        case .multi:
            circle.fillColor = GameConstants.green
            innerCircle.fillColor = GameConstants.white
        }

        circle.strokeColor = .clear
        innerCircle.strokeColor = .clear

        addChild(circle)
        addChild(innerCircle)
    }
}
